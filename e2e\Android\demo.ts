/**
 * 起点阅读自动化测试脚本
 *
 * 该脚本自动执行起点阅读App的日常任务，包括：
 * 1. 领取福利和签到
 * 2. 参与抽奖活动
 * 3. 观看激励视频获取奖励
 * 4. 周日兑换章节卡
 */

// 使用集中环境配置模块，不再直接导入和配置dotenv
import '../../config/env';
import { log } from './utils/common';
import { setupExitHandlers, shouldExit, cleanupAndExit } from './utils/exit-handler';
import { DeviceManager } from './utils/device-manager';
import { QidianReaderTester } from './agents/qidian-reader-tester';

// 设置退出处理程序
setupExitHandlers();

Promise.resolve(
  (async () => {
    // 创建设备管理器实例
    const deviceManager = new DeviceManager();
    
    try {
      log('开始起点阅读自动化任务...', 'info');
      
      // 连接设备并创建代理
      const agent = await deviceManager.initAndConnect(
        '如果出现位置、权限、用户协议等弹窗，点击同意。如果出现登录页面，关闭它。如果出现弹窗广告，关闭它'
      );
      
      // 创建起点阅读测试代理实例
      const qidianTester = new QidianReaderTester(agent);
      
      // --- 测试流程开始 ---
      
      // 执行完整的起点阅读自动化流程
      await qidianTester.runFullTest();
      
      log('所有起点阅读任务已成功完成！', 'success');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      log(`测试执行过程中发生错误: ${errorMessage}`, 'error');
      console.error(error);
      throw error; // 重新抛出错误，以便测试运行器能捕获到失败状态
    } finally {
      // 确保资源被释放
      try {
        await deviceManager.cleanup();
      } catch (cleanupError) {
        const errorMessage = cleanupError instanceof Error ? cleanupError.message : String(cleanupError);
        log(`释放设备资源时出错: ${errorMessage}`, 'error');
      }
      
      // 如果是因SIGINT中断而退出
      if (shouldExit) {
        cleanupAndExit('程序因SIGINT优雅退出');
      }
    }
  })()
);
