# SEO测试数据

## 文件结构

- `seo-test-data.ts`: 统一导出文件，提供向后兼容性
- `base-seo-data.ts`: 基础类和通用工具方法
- `seo-data/`: 包含各模块的SEO测试数据
  - `shouye.ts`: 首页
  - `sousuo.ts`: 搜索
  - `xiangmu.ts`: 找项目
  - `renmai.ts`: 人脉广场
  - `huodong.ts`: 聚活动
  - `chanye.ts`: 产业服务
  - `zhengce.ts`: 惠企政策
  - `ketang.ts`: 创业课堂
  - `zixun.ts`: 产业资讯
  - `zijin.ts`: 资金服务
  - `qiye.ts`: 企业服务
  - `jiandan.ts`: 简单页面（企业诊断、联营服务、AI落地页）
  - `zhaoshang.ts`: 招商服务
  - `index.ts`: 模块索引文件

## 使用方法

### 方法1：通过统一导出使用（向后兼容）

```typescript
import { SEOTestData } from "../../data/seo-test-data";

// 使用方式保持不变
const testData = SEOTestData.homePage;
```

### 方法2：直接导入特定模块（推荐）

```typescript
import { ShouyeSEOData } from "../../data/seo-data/shouye";
// 或者使用索引文件
import { ShouyeSEOData } from "../../data/seo-data";

// 直接从对应模块引用
const testData = ShouyeSEOData.homePage;
```

## 拆分目的

原始的SEO测试数据文件(`seo-test-data.ts`)已经变得过于庞大（2000+行），给维护和扩展带来困难。通过拆分为多个模块文件，我们实现了以下目标：

1. **提高可维护性**: 每个模块文件都独立聚焦于特定的功能区域
2. **简化扩展**: 添加新的测试数据只需要在对应模块文件中添加，不会影响其他模块
3. **保持兼容性**: 通过统一导出文件保证现有测试仍然可以正常工作
4. **改进代码组织**: 清晰的模块化结构使代码更易于理解和导航

## 扩展支持

此结构设计支持以下扩展：

1. **添加新的测试变体**: 例如，针对移动端添加特定的SEO测试数据

```typescript
// 在shouye.ts中添加移动端测试数据
export class ShouyeSEOData extends BaseSEOData {
  // PC端首页
  static homePage = { /* ... */ };

  // 移动端首页
  static homePageMobile = { /* ... */ };
}
```

2. **针对特定模块增加辅助方法**:

```typescript
export class XiangmuSEOData extends BaseSEOData {
  // 原有数据
  static projectListPage = { /* ... */ };

  // 新增辅助方法
  static generateCombinedTestData(regions, industries, amount) {
    // 生成组合测试数据的复杂逻辑
    return { /* ... */ };
  }
}
```

3. **支持更灵活的参数化测试**:

```typescript
// 在zhengce.ts中
export class ZhengceSEOData extends BaseSEOData {
  // 测试数据配置
  static testConfig = {
    regions: ['浙江省', '上海市', '北京市'],
    industries: ['人工智能', '新材料', '生物医药'],
    policyTypes: ['人才补贴', '研发投入', '税收优惠']
  };

  // 测试数据生成方法
  static generateAllCombinations() {
    // 根据配置生成所有组合的测试数据
  }
}
```

## 迁移计划

由于SEO测试数据已经拆分为多个文件，我们采用了以下迁移策略：

1. **创建基础结构**: 包括基础类文件、目录结构和索引文件
2. **逐个模块迁移**: 从原始文件中提取并移动代码到对应的模块文件
3. **创建统一导出**: 确保现有测试可继续运行
4. **替换原始文件**: 完成所有模块迁移后，用新的统一导出文件替换原始文件

迁移过程保证了测试的连续性，同时改进了代码组织结构。 