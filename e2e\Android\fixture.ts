import { test as base } from "@playwright/test";
import type { AndroidAgent } from '@midscene/android';
import { PlaywrightAiFixture, PlayWrightAiFixtureType } from "@midscene/web/playwright";
import { DeviceManager } from './utils/device-manager';
// 使用集中环境配置模块，不再直接导入和配置dotenv
import '../../config/env';

// 定义Android测试的Fixture类型
type AndroidFixtureType = {
  deviceManager: DeviceManager;
  agent: AndroidAgent;
};

// 扩展测试固件，添加Android相关的fixture
export const test = base.extend<PlayWrightAiFixtureType & AndroidFixtureType>({
  ...PlaywrightAiFixture(),
  
  // 设备管理器fixture
  deviceManager: async ({}, use) => {
    const deviceManager = new DeviceManager();
    await use(deviceManager);
    // 测试结束后清理资源
    await deviceManager.cleanup();
  },
  
  // Android代理fixture
  agent: async ({ deviceManager }, use) => {
    const agent = await deviceManager.initAndConnect(
      '如果出现位置、权限、用户协议等弹窗，点击同意。如果出现登录页面，等待扫码。如果出现更新提示，选择稍后或取消。'
    );
    await use(agent);
  },
});

// 导出expect用于断言
export { expect } from "@playwright/test";