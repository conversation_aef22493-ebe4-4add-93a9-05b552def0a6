// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption } from "../../common/test-helpers";
import * as fs from 'fs';
import * as path from 'path';
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";

let currentFileReportItems: SEOReportItem[] = [];
// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'huodong';

test.describe('企座网站聚活动SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('聚活动-频道页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.activityChannelPage;

    await page.goto(URL_CONFIG.activity);
    await page.waitForLoadState('networkidle');

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;
    (testInfo as any).currentPageForReport = page;

    console.log(`聚活动频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `聚活动频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('聚活动-列表页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.activityListPage;

    await page.goto(`${URL_CONFIG.activity}/list`);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;
    (testInfo as any).currentPageForReport = page;

    console.log(`聚活动列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `聚活动列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('聚活动-列表页-举办地点筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试举办地点筛选聚活动列表页');

    const locations: FilterOption[] = [
      { name: '北京市', value: '北京市', pinyin: 'beijingshi' }
    ];
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.activity}/list`,
      filterOptions: locations,
      getTestData: (option) => SEOTestData.activityListPageLocationFilter(option.value, option.pinyin || ''),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"举办地点选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('聚活动-详情页SEO元素验证', async ({ page, aiQuery, aiTap }, testInfo: TestInfo) => {
    console.log('测试聚活动详情页');

    await page.goto(`${URL_CONFIG.activity}/detail/462621239843426304`);
    await page.waitForLoadState('networkidle');

    // 直接在详情页获取活动信息
    const activityName = '第517届中国独角兽商机共享大会暨低空经济发展研讨会(2025年6月5-7日南京）';
    const activityProvince = '江苏省';
    const activityCity = '南京市';
    const activityType = '传统大会';
    
    console.log(`获取到详情页信息：活动名称=${activityName}, 省份=${activityProvince}, 类型=${activityType}`);
    
    // 等待页面完全加载
    await page.waitForTimeout(2000);
    (testInfo as any).currentPageForReport = page;

    const currentUrl = page.url();
    const activityId = currentUrl.split('/').pop()?.split('?')[0] || 'activity123';

    const detailTestData = SEOTestData.activityDetailPage(
      activityName,
      activityId,
      activityProvince,
      activityCity,
      activityType
    );
    
    const seoValidatorForNewPage = new SEOValidatorPage(page);
    const result = await seoValidatorForNewPage.validateTDKWithSoftAssertions({
      title: detailTestData.title,
      keywords: Array.isArray(detailTestData.keywords) ? detailTestData.keywords : [detailTestData.keywords].filter(Boolean),
      description: detailTestData.description,
      url: detailTestData.url
    });
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    console.log(`聚活动详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${Array.isArray(detailTestData.keywords) ? detailTestData.keywords.join(', ') : detailTestData.keywords}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);
    expect(result.success, `聚活动详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 