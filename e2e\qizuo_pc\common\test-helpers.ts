import { Page, TestInfo } from '@playwright/test';
import { SEOValidatorPage } from '../pages/seo-validator.page';
import type { SEOReportItem } from './base-config';

/**
 * 筛选条件选项接口
 */
export interface FilterOption {
  name: string;
  value: string;
  pinyin?: string;
}

/**
 * 筛选测试参数接口
 */
export interface FilterTestParams {
  page: Page;
  seoValidator: SEOValidatorPage;
  baseUrl: string;
  filterOptions: FilterOption[];
  getTestData: (option: FilterOption) => any;
  tapFilter: (option: FilterOption) => Promise<void>;
  currentFileReportItems: SEOReportItem[];
  baseTestCaseTitle: string;
  testInfo?: TestInfo;
  cleanupFilter?: (option: FilterOption) => Promise<void>;
}

/**
 * 筛选错误接口
 */
export interface FilterError {
  type: string;
  errors: string[];
}

/**
 * 通用筛选测试函数
 * @param params 筛选测试参数
 * @returns 错误数组
 */
export async function testSEOWithFilter(params: FilterTestParams): Promise<FilterError[]> {
  const { page, seoValidator, baseUrl, filterOptions, getTestData, tapFilter, currentFileReportItems, baseTestCaseTitle, testInfo, cleanupFilter } = params;
  
  // 访问基础页面
  await page.goto(baseUrl);
  await page.waitForLoadState('networkidle');
  
  const allErrors: FilterError[] = [];
  
  // 遍历所有筛选选项
  for (const option of filterOptions) {
      console.log(`测试筛选条件: ${option.name}`);
      
      // 点击筛选选项
      await tapFilter(option);
      await page.waitForLoadState('networkidle');
      
      // 获取测试数据并验证SEO元素
      const testData = getTestData(option);
      const result = await seoValidator.validateTDKWithSoftAssertions(testData);
      
      // 如果提供了testInfo，将测试数据和验证结果附加到testInfo对象上
      if (testInfo) {
        // 为每个选项都存储测试数据到testInfo，使用选项名称作为键的一部分
        // 修改安全名称生成方式，使用原始名称但移除特殊字符
        const safeOptionName = option.name
          .replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '') // 保留中英文字符和数字
          .substring(0, 30); // 限制长度
          
        (testInfo as any)[`seoTestData_${safeOptionName}`] = testData;
        (testInfo as any)[`seoValidationResult_${safeOptionName}`] = result;
        
        console.log(`[DEBUG] testSEOWithFilter: 已将测试数据附加到testInfo对象，选项：${option.name}，数据有效性：${!!testData && !!result}`);
      }
      
      // 构造并推送 SEOReportItem
      const reportItem: SEOReportItem = {
        testCaseTitle: `${baseTestCaseTitle} - ${option.name}`,
        status: result.success ? 'Pass' : 'Fail',
        expectedTitle: testData.title,
        actualTitle: result.actualElements.title,
        expectedKeywords: Array.isArray(testData.keywords) ? testData.keywords.join(', ') : String(testData.keywords || ''),
        actualKeywords: result.actualElements.keywords ? result.actualElements.keywords.split(/[\s,]+/).map(k => k.trim()).filter(k => k).join(', ') : '',
        expectedDescription: testData.description,
        actualDescription: result.actualElements.description,
        url: result.actualElements.url,
        expectedUrl: testData.url,
        errors: result.errors.join('; ')
      };
      currentFileReportItems.push(reportItem);
      console.log(`[DEBUG] testSEOWithFilter: 已添加报告项：${reportItem.testCaseTitle}`);
      
      // 记录结果
      console.log(`${option.name}筛选SEO验证结果: ${result.success ? '通过' : '失败'}`);
      
      // 收集错误
      if (!result.success) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        allErrors.push({
          type: `${option.name}筛选`,
          errors: result.errors
        });
      }
      
      // 详细输出验证的TDK信息
      console.log(`验证的${option.name}TDK信息:`);
      console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
      console.log(`描述: ${testData.description}`);
      console.log(`URL: ${testData.url}`);
      
      // 清除筛选条件
      if (cleanupFilter) {
        // 如果提供了自定义清理函数，使用它
        await cleanupFilter(option);
      } else {
        // 否则使用默认方法（再次点击同一个筛选选项）
        await tapFilter(option);
      }
      await page.waitForLoadState('networkidle');
  }
  
  return allErrors;
}

/**
 * 格式化错误信息
 * @param allErrors 错误数组
 * @returns 格式化后的错误字符串
 */
export function formatErrors(allErrors: FilterError[]): string {
  return allErrors.map(err => 
    `${err.type}SEO验证失败:\n${err.errors.join('\n')}`
  ).join('\n\n');
}

/**
 * 安全获取AI查询结果
 * @param aiQuery AI查询函数
 * @param query 查询字符串
 * @param fallback 默认值
 * @returns 查询结果或默认值
 */
export async function safeAIQuery(aiQuery: Function, query: string, fallback: string): Promise<string> {
  try {
    const result = await aiQuery(query);
    return result || fallback;
  } catch {
    return fallback;
  }
} 