// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption, safeAIQuery } from "../../common/test-helpers";
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";
import { chineseToPinyin } from '../../common/pinyin-utils';
import * as fs from 'fs';
import * as path from 'path';

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'kecheng';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站创业课堂SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('创业课堂-频道页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.courseChannelPage;

    await page.goto(URL_CONFIG.course);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`创业课堂频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    expect(result.success, `创业课堂频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('创业课堂-列表页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.courseListPage;

    await page.goto(`${URL_CONFIG.course}/list`);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`创业课堂列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `创业课堂列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('创业课堂-列表页-课程分类筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试课程分类筛选');

    const categoryNames = ['管理技巧', '人才培养'];
    const categories: FilterOption[] = categoryNames.map(name => ({
      name,
      value: name,
      pinyin: chineseToPinyin(name) as string
    }));
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.course}/list`,
      filterOptions: categories,
      getTestData: (option) => SEOTestData.courseListPageCategoryFilter(option.value, option.pinyin || ''),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"课程分类选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('创业课堂-列表页-课程标签筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试课程标签筛选');

    const tagNames = ['政策解读', '业务分析'];
    const tags: FilterOption[] = tagNames.map(name => ({
      name,
      value: name,
      pinyin: chineseToPinyin(name) as string
    }));
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.course}/list`,
      filterOptions: tags,
      getTestData: (option) => SEOTestData.courseListPageTagFilter(option.value, option.pinyin || ''),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"课程标签选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('创业课堂-列表页-适合岗位筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试适合岗位筛选');

    const positionNames = ['管理高阶', '管理中阶'];
    const positions: FilterOption[] = positionNames.map(name => ({
      name,
      value: name,
      pinyin: chineseToPinyin(name) as string
    }));
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.course}/list`,
      filterOptions: positions,
      getTestData: (option) => SEOTestData.courseListPagePositionFilter(option.value, option.pinyin || ''),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"适合岗位选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('创业课堂-详情页SEO元素验证', async ({ page, aiQuery }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试创业课堂详情页');

    await page.goto(`${URL_CONFIG.course}/detail/1470591643423444993`);
    await page.waitForLoadState('networkidle');
    
    // 等待3秒钟，确保页面内容完全加载和渲染
    console.log('等待3秒钟，确保页面完全加载...');
    await page.waitForTimeout(3000);
    console.log('页面已加载完成。');
    (testInfo as any).currentPageForReport = page;
    
    const currentUrl = page.url();
    const courseId = currentUrl.split('/').pop()?.split('?')[0] || '1470591643423444993';
    const courseName = '如何做好客户开发';
    const coursePosition ='销售管理';
    const courseCategory = '客户管理';
    const courseTag = '客户开发';
    const courseIntro = '要做好销售工作，首先必须做好的就是客户的开发。可以说客户开发的成功与否决定了销售的成败，因此，作为一名合格的销售人员，做好客户开发工作是重中之重。本课程分享了客户开发的方法技巧，着重解析客户开发的重要性、条件以及方法、渠道，引导销售人员做好客户开发的前期准备，为成功开发客户打下坚实基础。';
    
    const detailTestData = SEOTestData.courseDetailPage(
      courseName,
      courseId,
      coursePosition,
      courseCategory,
      courseTag,
      courseIntro
    );

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: detailTestData.title,
      keywords: Array.isArray(detailTestData.keywords) ? detailTestData.keywords : [detailTestData.keywords].filter(Boolean),
      description: detailTestData.description,
      url: detailTestData.url
    });
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    console.log(`创业课堂详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${Array.isArray(detailTestData.keywords) ? detailTestData.keywords.join(', ') : detailTestData.keywords}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);
    expect(result.success, `创业课堂详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('创业课堂-成长班列表页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.courseGrowthClassListPage;

    await page.goto(`${URL_CONFIG.course}/chengzhangban`);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`创业课堂成长班列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `创业课堂成长班列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('创业课堂-成长班详情页SEO元素验证', async ({ page, aiQuery }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试创业课堂成长班详情页');

    await page.goto(`${URL_CONFIG.course}/chengzhangban/1544200463237406722,1468823424359960578,1470999250485768193,1470946382715654146,1470937358867554305,1470635962181586945,1468489013310099457&总经理胜任成长班`);
    await page.waitForLoadState('networkidle');

    // 等待3秒钟，确保页面内容完全加载和渲染
    console.log('等待3秒钟，确保页面完全加载...');
    await page.waitForTimeout(3000);
    console.log('页面已加载完成。');
    (testInfo as any).currentPageForReport = page;

    const currentUrl = page.url();
    const classId = currentUrl.split('/').pop()?.split('?')[0] || 'class123';
    const className = await safeAIQuery(aiQuery, '成长班的名称', '示例成长班');
    const classIntro = await safeAIQuery(aiQuery, '成长班的简介', '这是一个优秀的成长班');

    const detailTestData = SEOTestData.courseGrowthClassDetailPage(
      className,
      classId
    );

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: detailTestData.title,
      keywords: Array.isArray(detailTestData.keywords) ? detailTestData.keywords : [detailTestData.keywords].filter(Boolean),
      description: detailTestData.description,
      url: detailTestData.url
    });
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    console.log(`创业课堂成长班详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${Array.isArray(detailTestData.keywords) ? detailTestData.keywords.join(', ') : detailTestData.keywords}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);
    expect(result.success, `创业课堂成长班详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 