/**
 * 环境变量管理模块
 * 
 * 此文件负责集中加载和管理项目中的环境变量，
 * 确保环境变量只被加载一次，避免多次加载导致的冲突。
 */

import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';

// 全局标记，跟踪环境变量是否已加载
let envLoaded = false;

/**
 * 加载环境变量
 * @param options 选项
 */
export function loadEnv(options: { force?: boolean } = {}) {
  // 如果环境变量已加载且非强制模式，则跳过
  if (envLoaded && !options.force) {
    console.log('⚠️ 环境变量已加载，跳过重复加载。使用 force: true 选项可强制重新加载。');
    return;
  }

  // 找到项目根目录中的 .env 文件
  const envPath = findEnvFile();
  
  // 加载环境变量
  const result = dotenv.config({
    path: envPath,
    override: true, // 始终覆盖，确保一致性
  });

  if (result.error) {
    console.error('❌ 加载环境变量失败:', result.error);
    return;
  }

  // 验证视觉模型配置
  validateVisionModelConfig();
  
  // 标记环境变量已加载
  envLoaded = true;
  console.log('🚀 环境变量加载完成，路径:', envPath);
}

/**
 * 查找 .env 文件
 */
function findEnvFile(): string {
  // 首先尝试当前工作目录
  let currentDir = process.cwd();
  let envPath = path.join(currentDir, '.env');
  
  // 检查是否存在 .env 文件
  if (fs.existsSync(envPath)) {
    return envPath;
  }
  
  // 如果没有找到，向上查找直到找到或达到文件系统根目录
  let prevDir = '';
  while (currentDir !== prevDir) {
    envPath = path.join(currentDir, '.env');
    if (fs.existsSync(envPath)) {
      return envPath;
    }
    prevDir = currentDir;
    currentDir = path.dirname(currentDir);
  }
  
  // 如果没有找到，返回默认路径
  return path.join(process.cwd(), '.env');
}

/**
 * 验证视觉模型配置，确保只启用一个视觉模型
 */
function validateVisionModelConfig() {
  const enabledModels = [];
  
  // 检查是否启用了豆包视觉模型
  if (isModelEnabled('MIDSCENE_USE_DOUBAO_VISION')) {
    enabledModels.push('MIDSCENE_USE_DOUBAO_VISION');
  }
  
  // 检查是否启用了千问视觉模型
  if (isModelEnabled('MIDSCENE_USE_QWEN_VL')) {
    enabledModels.push('MIDSCENE_USE_QWEN_VL');
  }
  
  // 检查是否启用了Gemini模型
  if (isModelEnabled('MIDSCENE_USE_GEMINI')) {
    enabledModels.push('MIDSCENE_USE_GEMINI');
  }
  
  // 如果启用了多个模型，发出警告
  if (enabledModels.length > 1) {
    console.warn(`⚠️ 检测到多个视觉模型同时启用: ${enabledModels.join(', ')}。这可能会导致冲突。`);
    console.warn('建议在 .env 文件中确保只启用一个视觉模型。');
  } else if (enabledModels.length === 1) {
    console.log(`✅ 当前启用的视觉模型: ${enabledModels[0]}`);
  } else {
    console.warn('⚠️ 没有检测到启用的视觉模型。');
  }
}

/**
 * 检查指定环境变量是否被启用
 */
function isModelEnabled(envName: string): boolean {
  const value = process.env[envName];
  if (!value) return false;
  
  // 转换为布尔值的逻辑，与 @midscene/shared 中的 getAIConfigInBoolean 保持一致
  if (/^(true|1)$/i.test(value)) {
    return true;
  }
  if (/^(false|0)$/i.test(value)) {
    return false;
  }
  return !!value.trim();
}

// 在导入时立即加载环境变量（默认行为）
loadEnv();

// 导出 NODE_ENV 工具函数
export const isProduction = () => process.env.NODE_ENV === 'production';
export const isDevelopment = () => process.env.NODE_ENV === 'development';
export const isTest = () => process.env.NODE_ENV === 'test';

// 导出 QIZUO_ENV 工具函数
export const isProductionEnv = () => process.env.QIZUO_ENV === 'production';
export const isTestEnv = () => process.env.QIZUO_ENV === 'test' || !isProductionEnv(); 