{"name": "playwright-demo", "private": true, "version": "0.0.1", "type": "module", "scripts": {"e2e": "playwright test --config=playwright.config.ts", "e2e:cache": "cross-env MIDSCENE_CACHE=true playwright test --config=playwright.config.ts", "e2e:ui": "playwright test --config=playwright.config.ts --ui", "e2e:ui:cache": "cross-env MIDSCENE_CACHE=true playwright test --config=playwright.config.ts --ui", "postinstall": "pnpm exec playwright install", "qidian": "tsx e2e/Android/demo.ts", "wecom": "tsx e2e/Android/wecom-ai-workspace.ts", "update-auth:pc": "tsx scripts/update-auth.ts pc", "update-auth:admin": "tsx scripts/update-auth.ts admin"}, "devDependencies": {"@midscene/android": "^0.20.1", "@midscene/web": "^0.20.1", "@playwright/test": "^1.54.1", "@types/glob": "8.1.0", "@types/jest": "~29.5.14", "@types/js-yaml": "4.0.9", "@types/node": "22.15.29", "cross-env": "7.0.3", "dotenv": "16.5.0", "eslint-plugin-prettier": "~5.4.1", "glob": "11.0.2", "js-yaml": "4.1.0", "rimraf": "~6.0.1", "ts-node": "10.9.2", "tsx": "^4.20.3", "typescript": "~5.8.3"}, "publishConfig": {"access": "public"}, "dependencies": {"pinyin-pro": "3.26.0", "punycode": "2.3.1", "yargs": "18.0.0"}}