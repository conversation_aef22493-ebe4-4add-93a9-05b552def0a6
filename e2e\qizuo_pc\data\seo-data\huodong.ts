/**
 * 聚活动SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class HuodongSEOData extends BaseSEOData {
  // 聚活动-频道页
  static activityChannelPage: SEOTestDataItem = {
    title: "项目合作考察会 创业项目路演 行业私董会 企业家高端康养会 - 企座",
    keywords: [
      "项目合作考察",
      "创业项目路演",
      "行业私董会",
      "企业家高端康养会",
      "在线活动报名",
      "在线查看活动议程"
    ],
    description: "全国各省市举办年均超过1000场项目合作考察会、创业项目路演、行业私董会、企业家高端康养会，支持在线筛选会议、了解议程、一键报名。快速锁定优质活动，拓展人脉与商机！",
    url: "huodong.aitojoy.com"
  };

  // 聚活动-列表页
  static activityListPage: SEOTestDataItem = {
    title: "精选企业家活动、创业路演活动推荐 - 企座",
    keywords: [
      "最新活动推荐",
      "热门活动推荐",
      "挑选适合活动",
      "线下会议日程",
      "掌握最新商机"
    ],
    description: "企座平台为企业家、创业项目每月在全国各地举办超过100场高规格线下商机合作活动。支持有意向的客户在线了解会议内容与议程、线上报名锁定席位。",
    url: "huodong.aitojoy.com/list"
  };

  /**
   * 聚活动-列表页-举办地点筛选
   * @param location 举办地点名称
   * @param locationPinyin 举办地点全拼
   * @returns SEO数据对象
   */
  static activityListPageLocationFilter = (location: string, locationPinyin: string) => {
    return {
      title: `精选${location}企业家活动、创业路演活动推荐 - 企座`,
      keywords: [
        `${location}最新活动推荐`,
        `${location}热门活动推荐`,
        `挑选${location}适合活动`,
        `${location}线下会议日程`
      ],
      description: `企座平台为企业家、创业项目在${location}举办高规格线下商机合作活动。支持有意向的客户在线了解会议内容与议程、线上报名锁定席位。`,
      url: `huodong.aitojoy.com/list/diqu-${locationPinyin}`
    };
  };

  /**
   * 聚活动-详情页
   * @param activityName 活动名称
   * @param activityId 活动ID
   * @param province 活动所在省份
   * @param city 活动所在城市
   * @param activityType 活动类型
   * @returns SEO数据对象
   */
  static activityDetailPage = (
    activityName: string,
    activityId: string,
    province: string = '',
    city: string = '',
    activityType: string = ''
  ) => {
    return {
      title: `${province}${city}${activityName} - 企座`,
      keywords: [
        `${activityName}嘉宾名单查询`,
        `${activityName}详细议程`,
        `立即在线预约${activityName}`
      ],
      description: `提供${province}${city}${activityType}${activityName}详细议程、嘉宾名单，立即在线预约，获取最新行业洞察与合作机会！`,
      url: `huodong.aitojoy.com/detail/${activityId}`
    };
  };
} 