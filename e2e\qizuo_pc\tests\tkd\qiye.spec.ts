// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption, safeAIQuery } from "../../common/test-helpers";
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";
import * as fs from 'fs';
import * as path from 'path';

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'qiye';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站企业服务SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('企业服务-频道页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.enterpriseServiceChannelPage;

    await page.goto(URL_CONFIG.enterpriseService);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`企业服务频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `企业服务频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('企业服务-列表页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.enterpriseServiceListPage;

    await page.goto(`${URL_CONFIG.enterpriseService}/list`);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`企业服务列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `企业服务列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('企业服务-详情页SEO元素验证', async ({ page, aiQuery }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试企业服务详情页');

    await page.goto(`${URL_CONFIG.enterpriseService}/detail/395285811587313664`);
    await page.waitForLoadState('networkidle');

    // 等待3秒钟，确保页面内容完全加载和渲染
    console.log('等待3秒钟，确保页面完全加载...');
    await page.waitForTimeout(3000);
    console.log('页面已加载完成。');
    (testInfo as any).currentPageForReport = page;

    const currentUrl = page.url();
    const serviceId = currentUrl.split('/').pop()?.split('?')[0] || 'service123';
    const serviceCategory = '工商财税';
    const serviceName = await safeAIQuery(aiQuery, '服务的名称', '');
    const serviceIntro = await safeAIQuery(aiQuery, '服务名称下方的服务简介', '');

    const detailTestData = SEOTestData.enterpriseServiceDetailPage(
      serviceCategory,
      serviceName,
      serviceId,
      serviceIntro
    );

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: detailTestData.title,
      keywords: Array.isArray(detailTestData.keywords) ? detailTestData.keywords : [detailTestData.keywords].filter(Boolean),
      description: detailTestData.description,
      url: detailTestData.url
    });
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    console.log(`企业服务详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${Array.isArray(detailTestData.keywords) ? detailTestData.keywords.join(', ') : detailTestData.keywords}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);
    expect(result.success, `企业服务详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 