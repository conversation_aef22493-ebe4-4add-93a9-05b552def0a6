import { expect } from '@playwright/test';
import type { Page } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { BasePage } from '../../qizuo_pc/pages/base.page';

export class MeetingSystemPage extends BasePage {
  constructor(page: Page, aiFixtures: PlayWrightAiFixtureType) {
    super(page, aiFixtures);
  }

  /**
   * 导航到会议系统后台
   */
  async navigateToMeetingSystem() {
    // 使用AI点击导航到会议系统后台
    await this.aiTap('会议系统后台入口按钮');
    await this.aiWaitFor('会议系统后台页面已加载', { timeoutMs: 5000 });
  }

  /**
   * 验证用户按钮是否可见
   */
  async verifyUserButtonVisible() {
    // 使用AI断言验证工作台按钮是否可见
    await this.aiAssert('工作台按钮在页面上可见');
  }

  /**
   * 导航到会议活动
   */
  async navigateToMeetingActivity() {
    // 使用AI点击导航到会议活动
    await this.aiTap('会议活动链接');
    await this.aiWaitFor('会议活动页面已加载', { timeoutMs: 3000 });
  }

  /**
   * 导航到资源管理
   */
  async navigateToResourceManagement() {
    // 使用AI点击导航到资源管理
    await this.aiTap('资源管理菜单项');
    await this.aiWaitFor('资源管理页面已加载', { timeoutMs: 3000 });
  }

  /**
   * 导航到订单中心
   */
  async navigateToOrderCenter() {
    // 使用AI点击导航到订单中心
    await this.aiTap('订单中心菜单项');
    await this.aiWaitFor('订单中心页面已加载', { timeoutMs: 3000 });
  }

  /**
   * 导航到促销管理
   */
  async navigateToPromotionManagement() {
    // 使用AI点击导航到促销管理
    await this.aiTap('促销管理菜单项');
    await this.aiWaitFor('促销管理页面已加载', { timeoutMs: 3000 });
  }

  /**
   * 导航到设置中心
   */
  async navigateToSettingCenter() {
    // 使用AI点击导航到设置中心
    await this.aiTap('设置中心菜单项');
    await this.aiWaitFor('设置中心页面已加载', { timeoutMs: 3000 });
  }
}