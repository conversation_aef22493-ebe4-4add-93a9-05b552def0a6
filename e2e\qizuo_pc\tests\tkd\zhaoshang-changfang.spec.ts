// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, type FilterOption } from "../../common/test-helpers";
import { chineseToPinyin } from "../../common/pinyin-utils";
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";
import * as fs from 'fs';
import * as path from 'path';

// 当前文件的测试结果集合
let currentFileReportItems: SEOReportItem[] = [];
const SOURCE_FILE = 'zhaoshang-changfang.spec.ts';

test.describe('企座网站招商服务-厂房SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('招商服务-厂房列表页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
      const seoValidator = new SEOValidatorPage(page);
      const testData = SEOTestData.zhaoshangFactoryListPage;

      await page.goto(`${URL_CONFIG.zhaoshang}/changfang`);
      await page.waitForLoadState('networkidle');
      await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
      (testInfo as any).seoTestData = testData;
      (testInfo as any).seoValidationResult = result;

      console.log(`招商服务厂房列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
      }
      console.log('验证的TDK信息:');
      console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
      console.log(`描述: ${testData.description}`);
      console.log(`URL: ${testData.url}`);
      expect(result.success, `招商服务厂房列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('招商服务-厂房列表页-地区筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试厂房列表页-地区筛选');
    
    // 定义测试地区
    // 示例1：单一省份
    // 示例2：省市组合
    const regionTests = [
      { name: '上海市', province: '上海市', city: '' },
      { name: '浙江省金华市', province: '浙江省', city: '金华市' }
    ];
    
    // 映射为筛选选项
    const regions: FilterOption[] = regionTests.map(item => ({
      name: item.name,
      value: JSON.stringify(item),  // 将省市信息序列化为JSON字符串
      pinyin: '' // 不需要预计算拼音，函数内部会处理
    }));
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.zhaoshang}/changfang`,
      filterOptions: regions,
      getTestData: (option) => {
        const { province, city } = JSON.parse(option.value);
        return SEOTestData.zhaoshangFactoryListPageRegionFilter(province, city);
      },
      tapFilter: async (option) => {
        const { province, city } = JSON.parse(option.value);
        // 选择省份
        if (province) {
          await aiTap(`"${province}"省份选项`);
          await page.waitForLoadState('networkidle');
        }
        // 选择城市
        if (city) {
          await aiTap(`"${city}"城市选项`);
          await page.waitForLoadState('networkidle');
        }
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('招商服务-厂房列表页-厂房面积筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试厂房列表页-厂房面积筛选');
    
    // 定义测试面积区间
    const areaRanges: FilterOption[] = [
      { name: '1000㎡以下', value: '1000㎡以下', pinyin: '1000pingmiyixia' },
      { name: '1000㎡-2000㎡', value: '1000㎡-2000㎡', pinyin: '1000pingmizhi2000pingmi' }
    ];
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.zhaoshang}/changfang`,
      filterOptions: areaRanges,
      getTestData: (option) => SEOTestData.zhaoshangFactoryListPageAreaFilter(option.value),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"厂房面积选项`);
        await page.waitForLoadState('networkidle');
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('招商服务-厂房列表页-厂房租金筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试厂房列表页-厂房租金筛选');
    
    // 定义测试租金区间
    const rentRanges: FilterOption[] = [
      { name: '0.3元/㎡/天以下', value: '0.3元/㎡/天以下', pinyin: '0.3yuanyixia' },
      { name: '0.3元-0.6元/㎡/天', value: '0.3元-0.6元/㎡/天', pinyin: '0.3yuanzhi0.6yuan' }
    ];
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.zhaoshang}/changfang`,
      filterOptions: rentRanges,
      getTestData: (option) => SEOTestData.zhaoshangFactoryListPageRentFilter(option.value),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"厂房租金选项`);
        await page.waitForLoadState('networkidle');
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('招商服务-厂房列表页-组合筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
      const seoValidator = new SEOValidatorPage(page);
      console.log('测试厂房列表页-组合筛选');
      
      // 定义组合筛选条件
      const combinedFilters: FilterOption[] = [
        { 
          name: '浙江省金华市+1000㎡-2000㎡+0.3元/㎡/天以下', 
          value: JSON.stringify({
            province: '浙江省',
            city: '金华市',
            areaRange: '1000㎡-2000㎡',
            rentRange: '0.3元/㎡/天以下'
          })
        }
      ];
      
      const allErrors = await testSEOWithFilter({
        page,
        seoValidator,
        baseUrl: `${URL_CONFIG.zhaoshang}/changfang`,
        filterOptions: combinedFilters,
        getTestData: (option) => {
          const { province, city, areaRange, rentRange } = JSON.parse(option.value);
          return SEOTestData.zhaoshangFactoryListPageCombinedFilter(
            province,
            city,
            areaRange,
            rentRange
          );
        },
        tapFilter: async (option) => {
          const { province, city, areaRange, rentRange } = JSON.parse(option.value);
          
          // 选择省份
          await aiTap(`"${province}"省份选项`);
          await page.waitForLoadState('networkidle');
          
          // 选择城市
          await aiTap(`"${city}"城市选项`);
          await page.waitForLoadState('networkidle');
          
          // 选择面积
          await aiTap(`"${areaRange}"厂房面积选项`);
          await page.waitForLoadState('networkidle');
          
          // 选择租金
          await aiTap(`"${rentRange}"厂房租金选项`);
          await page.waitForLoadState('networkidle');
        },
        currentFileReportItems: currentFileReportItems,
        baseTestCaseTitle: testInfo.title,
        testInfo: testInfo,
        // 添加清理筛选的函数
        cleanupFilter: async (option) => {
          const { province, city, areaRange, rentRange } = JSON.parse(option.value);
          
          // 清除筛选条件（点击同一个选项可以取消选择）
          await aiTap(`"${province}"省份选项`);
          await page.waitForLoadState('networkidle');
          await aiTap(`"${city}"城市选项`);
          await page.waitForLoadState('networkidle');
          await aiTap(`"${areaRange}"厂房面积选项`);
          await page.waitForLoadState('networkidle');
          await aiTap(`"${rentRange}"厂房租金选项`);
          await page.waitForLoadState('networkidle');
        }
      });
      
      // 最终断言
      expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('招商服务-厂房详情页SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
      const seoValidator = new SEOValidatorPage(page);
      
      // 定义测试数据 - 这些将是期望值而不是注入值
      const factoryName = "安徽六安舒城县舒城五金产业园11#2层-1909㎡多层厂房"; // 示例名称，实际使用时应替换为真实值
      const factoryId = "1739920503404711936";  // 示例ID，实际使用时应替换为真实值
      const buildingArea = "1909";  // 示例面积，实际使用时应替换为真实值
      const region = "安徽省六安市舒城县";  // 示例地区，实际使用时应替换为真实值
      const testData = SEOTestData.zhaoshangFactoryDetailPage(
        factoryName,
        factoryId,
        buildingArea,
        region
      );
      
      // 访问实际的厂房详情页
      await page.goto(`${URL_CONFIG.zhaoshang}/detail/changfang/${factoryId}`);
      await page.waitForLoadState('networkidle');
      
      // 验证SEO元素 - 这里将实际页面与期望值进行比较
      const result = await seoValidator.validateTDKWithSoftAssertions(testData);
      (testInfo as any).seoTestData = testData;
      (testInfo as any).seoValidationResult = result;
      
      // 记录验证结果
      console.log(`招商服务-厂房详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
      }
      
      // 详细输出验证的TDK信息
      console.log('验证的TDK信息:');
      console.log(`期望标题: ${testData.title}`);
      console.log(`期望关键词: ${testData.keywords.join(', ')}`);
      console.log(`期望描述: ${testData.description}`);
      console.log(`期望URL: ${testData.url}`);
      
      // 断言是否全部通过
      expect(result.success, `厂房详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 