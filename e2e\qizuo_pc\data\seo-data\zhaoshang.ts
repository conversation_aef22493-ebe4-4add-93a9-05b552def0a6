/**
 * 招商服务SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class ZhaoshangSEOData extends BaseSEOData {
  // 招商服务-频道页
  static zhaoshangServiceChannelPage: SEOTestDataItem = {
    title: "企业智能选址平台 开发区/园区/厂房/仓库/土地推荐 开发区园区政策查询 园区招商合作 - 企座",
    keywords: [
      "企业智能选址工具",
      "开发区/园区推荐",
      "厂房仓库租赁",
      "土地转让",
      "开发区园区入驻政策",
      "园区招商合作"
    ],
    description: "企座招商服务依托30年企业资源积累，提供智能AI选址、全国产业园区/开发区/厂房/仓库/土地精准推荐，实时查询地方奖补政策，助力企业高效选址入驻。免费获取定制方案！",
    url: "zhaoshang.aitojoy.com"
  };

  // 招商服务-产业园区列表页
  static zhaoshangIndustrialParkListPage: SEOTestDataItem = {
    title: "产业园招商平台 按地区/招商行业找园区 查询园区入驻企业、奖补政策 - 企座",
    keywords: [
      "产业园区查询",
      "产业园区入驻企业查询",
      "产业园区奖补政策查询",
      "寻找某地区产业园区",
      "寻找某行业产业园区"
    ],
    description: "汇集全国高新技术园区、特色产业园区信息，提供园区产业配套、租金区间、空置面积等关键数据，AI智能筛选匹配企业需求。",
    url: "zhaoshang.aitojoy.com/chanyeyuanqu"
  };

  /**
   * 招商服务-产业园区详情页
   * @param industrialParkName 产业园区名称
   * @param industrialParkId 产业园区ID
   * @returns SEO数据对象
   */
  static zhaoshangIndustrialParkDetailPage = (
    industrialParkName: string,
    industrialParkId: string
  ) => {
    return {
      title: `${industrialParkName}产业布局、周边配套、已入驻企业、惠企政策 - 企座`,
      keywords: [
        `${industrialParkName}产业布局`,
        `${industrialParkName}周边配套`,
        `${industrialParkName}已入驻企业`,
        `${industrialParkName}惠企政策`
      ],
      description: `查看${industrialParkName}的产业布局、基础设施、重点企业及税收/人才/租金优惠政策。获取专属入驻咨询，提交需求享一对一服务。`,
      url: `zhaoshang.aitojoy.com/detail/chanyeyuanqu/${industrialParkId}`
    };
  };

  /**
   * 招商服务-产业园区列表页-所在省份/城市筛选
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangIndustrialParkListPageRegionFilter = (
    province: string = '',
    city: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 生成URL部分
    let provincePinyin = '';
    let cityPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    
    return {
      title: `${regionStr}产业园招商平台 查询园区入驻企业、奖补政策 - 企座`,
      keywords: [
        `${regionStr}产业园区查询`,
        `${regionStr}产业园区入驻企业查询`,
        `${regionStr}产业园区奖补政策查询`,
        `寻找${regionStr}产业园区`
      ],
      description: `汇集${regionStr}高新技术园区、特色产业园区信息，提供园区产业配套、租金区间、空置面积等关键数据，AI智能筛选匹配企业需求。`,
      url: `zhaoshang.aitojoy.com/chanyeyuanqu/diqu-${regionUrlPart}`
    };
  };

  /**
   * 招商服务-产业园区列表页-招商行业筛选
   * @param industry 招商行业名称
   * @returns SEO数据对象
   */
  static zhaoshangIndustrialParkListPageIndustryFilter = (
    industry: string
  ) => {
    // 使用拼音工具类将中文行业名称转换为拼音
    const industryPinyin = BaseSEOData.chineseToPinyin(industry) as string;
    
    return {
      title: `${industry}行业产业园招商平台 查询园区入驻企业、奖补政策 - 企座`,
      keywords: [
        `${industry}行业产业园区查询`,
        `${industry}行业产业园区入驻企业查询`,
        `${industry}行业产业园区奖补政策查询`,
        `寻找${industry}行业产业园区`
      ],
      description: `汇集${industry}行业高新技术园区、特色产业园区信息，提供园区产业配套、租金区间、空置面积等关键数据，AI智能筛选匹配企业需求。`,
      url: `zhaoshang.aitojoy.com/chanyeyuanqu/hangye-${industryPinyin}`
    };
  };

  /**
   * 招商服务-产业园区列表页-组合筛选（地区+行业）
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @param industry 招商行业名称，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangIndustrialParkListPageCombinedFilter = (
    province: string = '',
    city: string = '',
    industry: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 生成URL部分
    let provincePinyin = '';
    let cityPinyin = '';
    let industryPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    if (industry) {
      industryPinyin = BaseSEOData.chineseToPinyin(industry) as string;
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    const urlParts: string[] = [];
    
    if (regionStr) {
      urlParts.push(`diqu-${regionUrlPart}`);
    }
    
    if (industry) {
      urlParts.push(`hangye-${industryPinyin}`);
    }
    
    const urlPath = urlParts.join('-');
    
    return {
      title: `${regionStr}${industry}行业产业园招商平台 查询园区入驻企业、奖补政策 - 企座`,
      keywords: [
        `${regionStr}${industry}行业产业园区查询`,
        `${regionStr}${industry}行业产业园区入驻企业查询`,
        `${regionStr}${industry}行业产业园区奖补政策查询`,
        `寻找${regionStr}${industry}行业产业园区`
      ],
      description: `汇集${regionStr}${industry}行业高新技术园区、特色产业园区信息，提供园区产业配套、租金区间、空置面积等关键数据，AI智能筛选匹配企业需求。`,
      url: `zhaoshang.aitojoy.com/chanyeyuanqu/${urlPath}`
    };
  };

  // 招商服务-开发区列表页
  static zhaoshangDevelopmentZoneListPage: SEOTestDataItem = {
    title: "全国开发区信息库 按地区/招商行业找开发区 查询开发区入驻企业/奖补政策 - 企座",
    keywords: [
      "开发区查询",
      "开发区入驻企业查询",
      "开发区奖补政策查询",
      "寻找某地区开发区",
      "寻找某行业开发区"
    ],
    description: "实时更新全国开发区名单、产业定位、配套设施及入驻条件。支持按省份/城市筛选，查看税收优惠、人才补贴等政策，快速匹配优质开发区资源。",
    url: "zhaoshang.aitojoy.com/kaifaqu"
  };

  /**
   * 招商服务-开发区列表页-所在省份/城市筛选
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangDevelopmentZoneListPageRegionFilter = (
    province: string = '',
    city: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 生成URL部分
    // 使用拼音工具类将中文转换为拼音
    let provincePinyin = '';
    let cityPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    
    return {
      title: `${regionStr}开发区信息库 查询开发区入驻企业/奖补政策 - 企座`,
      keywords: [
        `${regionStr}开发区查询`,
        `${regionStr}开发区入驻企业查询`,
        `${regionStr}开发区奖补政策查询`,
        `寻找${regionStr}开发区`
      ],
      description: `实时更新${regionStr}开发区名单、产业定位、配套设施及入驻条件。支持按省份/城市筛选，查看税收优惠、人才补贴等政策，快速匹配优质开发区资源。`,
      url: `zhaoshang.aitojoy.com/kaifaqu/diqu-${regionUrlPart}`
    };
  };

  /**
   * 招商服务-开发区列表页-招商行业筛选
   * @param industry 招商行业名称
   * @returns SEO数据对象
   */
  static zhaoshangDevelopmentZoneListPageIndustryFilter = (
    industry: string
  ) => {
    // 使用拼音工具类将中文行业名称转换为拼音
    const industryPinyin = BaseSEOData.chineseToPinyin(industry) as string;
    
    return {
      title: `${industry}行业开发区信息库 查询开发区入驻企业/奖补政策 - 企座`,
      keywords: [
        `${industry}行业开发区查询`,
        `${industry}行业开发区入驻企业查询`,
        `${industry}行业开发区奖补政策查询`,
        `寻找${industry}行业开发区`
      ],
      description: `实时更新${industry}行业开发区名单、产业定位、配套设施及入驻条件。支持按省份/城市筛选，查看税收优惠、人才补贴等政策，快速匹配优质开发区资源。`,
      url: `zhaoshang.aitojoy.com/kaifaqu/hangye-${industryPinyin}`
    };
  };

  /**
   * 招商服务-开发区列表页-组合筛选（地区+行业）
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @param industry 招商行业名称，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangDevelopmentZoneListPageCombinedFilter = (
    province: string = '',
    city: string = '',
    industry: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 生成URL部分
    let provincePinyin = '';
    let cityPinyin = '';
    let industryPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    if (industry) {
      industryPinyin = BaseSEOData.chineseToPinyin(industry) as string;
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    const urlParts: string[] = [];
    
    if (regionStr) {
      urlParts.push(`diqu-${regionUrlPart}`);
    }
    
    if (industry) {
      urlParts.push(`hangye-${industryPinyin}`);
    }
    
    const urlPath = urlParts.join('-');
    
    return {
      title: `${regionStr}${industry}行业开发区信息库 查询开发区入驻企业/奖补政策 - 企座`,
      keywords: [
        `${regionStr}${industry}行业开发区查询`,
        `${regionStr}${industry}行业开发区入驻企业查询`,
        `${regionStr}${industry}行业开发区奖补政策查询`,
        `寻找${regionStr}${industry}行业开发区`
      ],
      description: `实时更新${regionStr}${industry}行业开发区名单、产业定位、配套设施及入驻条件。支持按省份/城市筛选，查看税收优惠、人才补贴等政策，快速匹配优质开发区资源。`,
      url: `zhaoshang.aitojoy.com/kaifaqu/${urlPath}`
    };
  };

  /**
   * 招商服务-开发区详情页
   * @param developmentZoneName 开发区名称
   * @param developmentZoneId 开发区ID
   * @returns SEO数据对象
   */
  static zhaoshangDevelopmentZoneDetailPage = (
    developmentZoneName: string,
    developmentZoneId: string
  ) => {
    return {
      title: `${developmentZoneName}产业布局、周边配套、已入驻企业、惠企政策、投资机构、区内产业园/土地 - 企座`,
      keywords: [
        `${developmentZoneName}产业布局`,
        `${developmentZoneName}周边配套`,
        `${developmentZoneName}已入驻企业`,
        `${developmentZoneName}惠企政策`,
        `${developmentZoneName}投资机构`,
        `${developmentZoneName}内产业园土地信息`
      ],
      description: `查看${developmentZoneName}的产业布局、基础设施、重点企业及税收/人才/租金优惠政策。获取专属入驻咨询，提交需求享一对一服务。`,
      url: `zhaoshang.aitojoy.com/kaifaqu/${developmentZoneId}`
    };
  };

  // 招商服务-厂房列表页
  static zhaoshangFactoryListPage: SEOTestDataItem = {
    title: "厂房出租信息平台 最新工业厂房房源 按地区/面积/租金/类型找厂房 - 企座",
    keywords: [
      "厂房出租",
      "寻找某地区厂房",
      "寻找某租金区间厂房",
      "厂房实景图",
      "厂房区位交通"
    ],
    description: "精准推荐优质工业厂房资源，支持按面积/层高/区位/租金筛选，查看厂房实景图、交通配套，降低企业选址成本。",
    url: "zhaoshang.aitojoy.com/changfang"
  };

  /**
   * 招商服务-厂房列表页-所在省份/城市筛选
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangFactoryListPageRegionFilter = (
    province: string = '',
    city: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 生成URL部分
    let provincePinyin = '';
    let cityPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    
    return {
      title: `${regionStr}厂房出租信息平台 最新工业厂房房源 - 企座`,
      keywords: [
        `${regionStr}厂房出租`,
        `${regionStr}厂房实景图`,
        `${regionStr}厂房区位交通`
      ],
      description: `精准推荐${regionStr}优质工业厂房资源，支持按面积/层高/区位/租金筛选，查看厂房实景图、交通配套，降低企业选址成本。`,
      url: `zhaoshang.aitojoy.com/changfang/diqu-${regionUrlPart}`
    };
  };

  /**
   * 招商服务-厂房列表页-厂房面积筛选
   * @param areaRange 厂房面积区间，如"1000㎡以下"、"1000㎡-2000㎡"
   * @returns SEO数据对象
   */
  static zhaoshangFactoryListPageAreaFilter = (areaRange: string) => {
    // 处理URL部分，将面积区间转换为URL格式
    // ㎡ -> pingmi, 以上 -> yishang, 以下 -> yixia, - -> zhi
    const areaRangeForUrl = areaRange
      .replace(/㎡/g, 'pingmi')
      .replace(/以上/g, 'yishang')
      .replace(/以下/g, 'yixia')
      .replace(/-/g, 'zhi');
    
    return {
      title: `${areaRange}厂房出租信息平台 最新工业厂房房源 - 企座`,
      keywords: [
        `${areaRange}厂房出租`,
        `${areaRange}厂房实景图`,
        `${areaRange}厂房区位交通`
      ],
      description: `精准推荐${areaRange}优质工业厂房资源，支持按面积/层高/区位/租金筛选，查看厂房实景图、交通配套，降低企业选址成本。`,
      url: `zhaoshang.aitojoy.com/changfang/mianji-${areaRangeForUrl}`
    };
  };

  /**
   * 招商服务-厂房列表页-厂房租金筛选
   * @param rentRange 厂房租金区间，如"0.3元每天以下"、"0.3元-0.6元每天"
   * @returns SEO数据对象
   */
  static zhaoshangFactoryListPageRentFilter = (rentRange: string) => {
    // 处理URL部分，将租金区间转换为URL格式
    // 元 -> yuan, 以上 -> yishang, 以下 -> yixia, - -> zhi
    // 注意：㎡不在全拼中体现
    const rentRangeForUrl = rentRange
      .replace(/元/g, 'yuan')
      .replace(/㎡/g, '')
      .replace(/\/+天/g, '') // 移除/天，处理可能的多个斜杠
      .replace(/以上/g, 'yishang')
      .replace(/以下/g, 'yixia')
      .replace(/-/g, 'zhi')
      .replace(/\s+/g, ''); // 移除可能的空格
    
    return {
      title: `${rentRange}厂房出租信息平台 最新工业厂房房源 - 企座`,
      keywords: [
        `${rentRange}厂房出租`,
        `${rentRange}厂房实景图`,
        `${rentRange}厂房区位交通`
      ],
      description: `精准推荐${rentRange}优质工业厂房资源，支持按面积/层高/区位/租金筛选，查看厂房实景图、交通配套，降低企业选址成本。`,
      url: `zhaoshang.aitojoy.com/changfang/zujin-${rentRangeForUrl}`
    };
  };

  /**
   * 招商服务-厂房列表页-组合筛选（地区+面积+租金）
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @param areaRange 厂房面积区间，可以为空
   * @param rentRange 厂房租金区间，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangFactoryListPageCombinedFilter = (
    province: string = '',
    city: string = '',
    areaRange: string = '',
    rentRange: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 组合所有筛选条件
    const combinedPrefix = `${regionStr}${areaRange}${rentRange}`;
    
    // 生成URL部分
    let provincePinyin = '';
    let cityPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    // 处理面积和租金的URL转换
    let areaRangeForUrl = '';
    if (areaRange) {
      areaRangeForUrl = areaRange
        .replace(/㎡/g, 'pingmi')
        .replace(/以上/g, 'yishang')
        .replace(/以下/g, 'yixia')
        .replace(/-/g, 'zhi');
    }
    
    let rentRangeForUrl = '';
    if (rentRange) {
      rentRangeForUrl = rentRange
        .replace(/元/g, 'yuan')
        .replace(/㎡/g, '')
        .replace(/\/+天/g, '') // 移除/天，处理可能的多个斜杠
        .replace(/以上/g, 'yishang')
        .replace(/以下/g, 'yixia')
        .replace(/-/g, 'wanyuanzhi')
        .replace(/\s+/g, '');
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    const urlParts: string[] = [];
    
    if (regionStr) {
      urlParts.push(`diqu-${regionUrlPart}`);
    }
    
    if (areaRange) {
      urlParts.push(`mianji-${areaRangeForUrl}`);
    }
    
    if (rentRange) {
      urlParts.push(`zujin-${rentRangeForUrl}`);
    }
    
    const urlPath = urlParts.join('-');
    
    return {
      title: `${combinedPrefix}厂房出租信息平台 最新工业厂房房源 - 企座`,
      keywords: [
        `${combinedPrefix}厂房出租`,
        `${combinedPrefix}厂房实景图`,
        `${combinedPrefix}厂房区位交通`
      ],
      description: `精准推荐${combinedPrefix}优质工业厂房资源，支持按面积/层高/区位/租金筛选，查看厂房实景图、交通配套，降低企业选址成本。`,
      url: `zhaoshang.aitojoy.com/changfang/${urlPath}`
    };
  };

  /**
   * 招商服务-厂房详情页
   * @param factoryName 厂房名称
   * @param factoryId 厂房ID
   * @param buildingArea 建筑面积
   * @param region 所属地区
   * @returns SEO数据对象
   */
  static zhaoshangFactoryDetailPage = (
    factoryName: string,
    factoryId: string,
    buildingArea: string,
    region: string
  ) => {
    return {
      title: `${factoryName}出租${buildingArea}㎡厂房 可查看厂房平面图、特色、周边配套介绍 - 企座`,
      keywords: [
        `了解${factoryName}面积、层高、承重、租金信息`,
        `了解${factoryName}平面图、特色信息`,
        `了解${factoryName}周边配套`
      ],
      description: `${factoryName}位于${region}，面积${buildingArea}㎡，查看厂房层高、承重、租金、平面图、周边配套等信息。`,
      url: `zhaoshang.aitojoy.com/detail/changfang/${factoryId}`
    };
  };

  // 招商服务-仓库列表页
  static zhaoshangWarehouseListPage: SEOTestDataItem = {
    title: "仓库招租 最新仓库出租信息 按地区/面积/租金找仓库 -企座",
    keywords: [
      "仓库出租",
      "寻找某地区仓库",
      "寻找某租金区间仓库",
      "仓库实景图",
      "仓库区位交通"
    ],
    description: "聚合全国仓储资源，涵盖高标仓、冷链库、普通仓库等类型，支持按面积/租金/区位一键筛选，提供仓库租赁信息、物业信息等详细参数",
    url: "zhaoshang.aitojoy.com/cangku"
  };

  /**
   * 招商服务-仓库列表页-所在省份/城市筛选
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangWarehouseListPageRegionFilter = (
    province: string = '',
    city: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 生成URL部分
    let provincePinyin = '';
    let cityPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    
    return {
      title: `${regionStr}仓库招租 最新仓库出租信息 -企座`,
      keywords: [
        `${regionStr}仓库出租`,
        `${regionStr}仓库实景图`,
        `${regionStr}仓库区位交通`
      ],
      description: `聚合${regionStr}仓储资源，涵盖高标仓、冷链库、普通仓库等类型，支持按面积/租金/区位一键筛选，提供仓库租赁信息、物业信息等详细参数`,
      url: `zhaoshang.aitojoy.com/cangku/diqu-${regionUrlPart}`
    };
  };

  /**
   * 招商服务-仓库列表页-仓库面积筛选
   * @param areaRange 仓库面积区间，如"1000㎡以下"、"1000㎡-2000㎡"
   * @returns SEO数据对象
   */
  static zhaoshangWarehouseListPageAreaFilter = (areaRange: string) => {
    // 处理URL部分，将面积区间转换为URL格式
    // ㎡ -> pingmi, 以上 -> yishang, 以下 -> yixia, - -> zhi
    const areaRangeForUrl = areaRange
      .replace(/㎡/g, 'pingmi')
      .replace(/以上/g, 'yishang')
      .replace(/以下/g, 'yixia')
      .replace(/-/g, 'zhi');
    
    return {
      title: `${areaRange}仓库招租 最新仓库出租信息 -企座`,
      keywords: [
        `${areaRange}仓库出租`,
        `${areaRange}仓库实景图`,
        `${areaRange}仓库区位交通`
      ],
      description: `聚合${areaRange}仓储资源，涵盖高标仓、冷链库、普通仓库等类型，支持按面积/租金/区位一键筛选，提供仓库租赁信息、物业信息等详细参数`,
      url: `zhaoshang.aitojoy.com/cangku/mianji-${areaRangeForUrl}`
    };
  };

  /**
   * 招商服务-仓库列表页-仓库租金筛选
   * @param rentRange 仓库租金区间，如"0.3元以下"、"0.3元-0.6元"
   * @returns SEO数据对象
   */
  static zhaoshangWarehouseListPageRentFilter = (rentRange: string) => {
    // 处理URL部分，将租金区间转换为URL格式
    // 元 -> yuan, 以上 -> yishang, 以下 -> yixia, - -> zhi
    // 注意：㎡不在全拼中体现，/天需要移除
    const rentRangeForUrl = rentRange
      .replace(/元/g, 'yuan')
      .replace(/㎡/g, '')
      .replace(/\/+天/g, '') // 移除/天，处理可能的多个斜杠
      .replace(/以上/g, 'yishang')
      .replace(/以下/g, 'yixia')
      .replace(/-/g, 'zhi')
      .replace(/\s+/g, ''); // 移除可能的空格
    
    return {
      title: `${rentRange}仓库招租 最新仓库出租信息 -企座`,
      keywords: [
        `${rentRange}仓库出租`,
        `${rentRange}仓库实景图`,
        `${rentRange}仓库区位交通`
      ],
      description: `聚合${rentRange}仓储资源，涵盖高标仓、冷链库、普通仓库等类型，支持按面积/租金/区位一键筛选，提供仓库租赁信息、物业信息等详细参数`,
      url: `zhaoshang.aitojoy.com/cangku/zujin-${rentRangeForUrl}`
    };
  };

  /**
   * 招商服务-仓库列表页-组合筛选（地区+面积+租金）
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @param areaRange 仓库面积区间，可以为空
   * @param rentRange 仓库租金区间，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangWarehouseListPageCombinedFilter = (
    province: string = '',
    city: string = '',
    areaRange: string = '',
    rentRange: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 组合所有筛选条件
    const combinedPrefix = `${regionStr}${areaRange}${rentRange}`;
    
    // 生成URL部分
    let provincePinyin = '';
    let cityPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    // 处理面积和租金的URL转换
    let areaRangeForUrl = '';
    if (areaRange) {
      areaRangeForUrl = areaRange
        .replace(/㎡/g, 'pingmi')
        .replace(/以上/g, 'yishang')
        .replace(/以下/g, 'yixia')
        .replace(/-/g, 'zhi');
    }
    
    let rentRangeForUrl = '';
    if (rentRange) {
      rentRangeForUrl = rentRange
        .replace(/元/g, 'yuan')
        .replace(/㎡/g, '')
        .replace(/\/+天/g, '') // 移除/天，处理可能的多个斜杠
        .replace(/以上/g, 'yishang')
        .replace(/以下/g, 'yixia')
        .replace(/-/g, 'zhi')
        .replace(/\s+/g, '');
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    const urlParts: string[] = [];
    
    if (regionStr) {
      urlParts.push(`diqu-${regionUrlPart}`);
    }
    
    if (areaRange) {
      urlParts.push(`mianji-${areaRangeForUrl}`);
    }
    
    if (rentRange) {
      urlParts.push(`zujin-${rentRangeForUrl}`);
    }
    
    const urlPath = urlParts.join('-');
    
    return {
      title: `${combinedPrefix}仓库招租 最新仓库出租信息 -企座`,
      keywords: [
        `${combinedPrefix}仓库出租`,
        `${combinedPrefix}仓库实景图`,
        `${combinedPrefix}仓库区位交通`
      ],
      description: `聚合${combinedPrefix}仓储资源，涵盖高标仓、冷链库、普通仓库等类型，支持按面积/租金/区位一键筛选，提供仓库租赁信息、物业信息等详细参数`,
      url: `zhaoshang.aitojoy.com/cangku/${urlPath}`
    };
  };

  /**
   * 招商服务-仓库详情页
   * @param warehouseName 仓库名称
   * @param warehouseId 仓库ID
   * @param buildingArea 建筑面积
   * @param region 所属地区
   * @returns SEO数据对象
   */
  static zhaoshangWarehouseDetailPage = (
    warehouseName: string,
    warehouseId: string,
    buildingArea: string,
    region: string
  ) => {
    return {
      title: `${warehouseName}出租${buildingArea}仓库 可查看仓库平面图、租赁信息、物业信息、特色、周边配套介绍 - 企座`,
      keywords: [
        `了解${warehouseName}面积、层高、租金信息`,
        `了解${warehouseName}平面图、特色信息`,
        `了解${warehouseName}周边配套`
      ],
      description: `${warehouseName}位于${region}，面积${buildingArea}㎡，查看仓库层高、租金、平面图、周边配套等信息。`,
      url: `zhaoshang.aitojoy.com/detail/cangku/${warehouseId}`
    };
  };

  // 招商服务-土地列表页
  static zhaoshangLandListPage: SEOTestDataItem = {
    title: "土地转让信息平台 按地区/面积/价格找土地 - 企座",
    keywords: [
      "土地价格查询",
      "按地区找土地",
      "园区土地出让",
      "土地投资要求查询",
      "土地区位交通"
    ],
    description: "实时更新政府招拍挂土地、园区工业用地转让信息，提供地价、投资要求及区位交通数据，助力企业拿地决策。",
    url: "zhaoshang.aitojoy.com/tudi"
  };

  /**
   * 招商服务-土地列表页-所在省份/城市筛选
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangLandListPageRegionFilter = (
    province: string = '',
    city: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 生成URL部分
    let provincePinyin = '';
    let cityPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    
    return {
      title: `${regionStr}土地转让信息平台 - 企座`,
      keywords: [
        `${regionStr}土地价格查询`,
        `${regionStr}园区土地出让`,
        `${regionStr}土地投资要求查询`,
        `${regionStr}土地区位交通`
      ],
      description: `实时更新${regionStr}政府招拍挂土地、园区工业用地转让信息，提供地价、投资要求及区位交通数据，助力企业拿地决策。`,
      url: `zhaoshang.aitojoy.com/tudi/diqu-${regionUrlPart}`
    };
  };

  /**
   * 招商服务-土地列表页-土地面积筛选
   * @param areaRange 土地面积区间，如"5亩以下"、"5亩-20亩"
   * @returns SEO数据对象
   */
  static zhaoshangLandListPageAreaFilter = (areaRange: string) => {
    // 处理URL部分，将面积区间转换为URL格式
    // 亩 -> mu, 以上 -> yishang, 以下 -> yixia, - -> zhi
    const areaRangeForUrl = areaRange
      .replace(/亩/g, 'mu')
      .replace(/以上/g, 'yishang')
      .replace(/以下/g, 'yixia')
      .replace(/-/g, 'zhi');
    
    return {
      title: `${areaRange}土地转让信息平台 - 企座`,
      keywords: [
        `${areaRange}土地价格查询`,
        `${areaRange}园区土地出让`,
        `${areaRange}土地投资要求查询`,
        `${areaRange}土地区位交通`
      ],
      description: `实时更新${areaRange}政府招拍挂土地、园区工业用地转让信息，提供地价、投资要求及区位交通数据，助力企业拿地决策。`,
      url: `zhaoshang.aitojoy.com/tudi/mianji-${areaRangeForUrl}`
    };
  };

  /**
   * 招商服务-土地列表页-土地价格筛选
   * @param priceRange 土地价格区间，如"8万元以下"、"8万元-12万元"
   * @returns SEO数据对象
   */
  static zhaoshangLandListPagePriceFilter = (priceRange: string) => {
    // 处理URL部分，将价格区间转换为URL格式
    // 万元 -> wanyuan, 以上 -> yishang, 以下 -> yixia, - -> zhi
    // 注意：亩不在全拼中体现
    const priceRangeForUrl = priceRange
      .replace(/万元/g, 'wanyuan')
      .replace(/亩/g, '')
      .replace(/\/+/g, '') // 移除可能的斜杠
      .replace(/以上/g, 'yishang')
      .replace(/以下/g, 'yixia')
      .replace(/-/g, 'wanyuanzhi')
      .replace(/\s+/g, ''); // 移除可能的空格
    
    return {
      title: `${priceRange}土地转让信息平台 - 企座`,
      keywords: [
        `${priceRange}土地价格查询`,
        `${priceRange}园区土地出让`,
        `${priceRange}土地投资要求查询`,
        `${priceRange}土地区位交通`
      ],
      description: `实时更新${priceRange}政府招拍挂土地、园区工业用地转让信息，提供地价、投资要求及区位交通数据，助力企业拿地决策。`,
      url: `zhaoshang.aitojoy.com/tudi/jiage-${priceRangeForUrl}`
    };
  };

  /**
   * 招商服务-土地列表页-组合筛选（地区+面积+价格）
   * @param province 省份名称，可以为空
   * @param city 城市名称，可以为空
   * @param areaRange 土地面积区间，可以为空
   * @param priceRange 土地价格区间，可以为空
   * @returns SEO数据对象
   */
  static zhaoshangLandListPageCombinedFilter = (
    province: string = '',
    city: string = '',
    areaRange: string = '',
    priceRange: string = ''
  ) => {
    // 组合省市名称
    const regionStr = `${province}${city}`;
    
    // 组合所有筛选条件
    const combinedPrefix = `${regionStr}${areaRange}${priceRange}`;
    
    // 生成URL部分
    let provincePinyin = '';
    let cityPinyin = '';
    
    if (province) {
      provincePinyin = BaseSEOData.chineseToPinyin(province) as string;
    }
    
    if (city) {
      cityPinyin = BaseSEOData.chineseToPinyin(city) as string;
    }
    
    // 处理面积和价格的URL转换
    let areaRangeForUrl = '';
    if (areaRange) {
      areaRangeForUrl = areaRange
        .replace(/亩/g, 'mu')
        .replace(/以上/g, 'yishang')
        .replace(/以下/g, 'yixia')
        .replace(/-/g, 'zhi');
    }
    
    let priceRangeForUrl = '';
    if (priceRange) {
      priceRangeForUrl = priceRange
        .replace(/万元/g, 'wanyuan')
        .replace(/亩/g, '')
        .replace(/\/+/g, '') // 移除可能的斜杠
        .replace(/以上/g, 'yishang')
        .replace(/以下/g, 'yixia')
        .replace(/-/g, 'zhi')
        .replace(/\s+/g, '');
    }
    
    // 组合URL部分
    const regionUrlPart = provincePinyin + cityPinyin;
    const urlParts: string[] = [];
    
    if (regionStr) {
      urlParts.push(`diqu-${regionUrlPart}`);
    }
    
    if (areaRange) {
      urlParts.push(`mianji-${areaRangeForUrl}`);
    }
    
    if (priceRange) {
      urlParts.push(`jiage-${priceRangeForUrl}`);
    }
    
    const urlPath = urlParts.join('-');
    
    return {
      title: `${combinedPrefix}土地转让信息平台 - 企座`,
      keywords: [
        `${combinedPrefix}土地价格查询`,
        `${combinedPrefix}园区土地出让`,
        `${combinedPrefix}土地投资要求查询`,
        `${combinedPrefix}土地区位交通`
      ],
      description: `实时更新${combinedPrefix}政府招拍挂土地、园区工业用地转让信息，提供地价、投资要求及区位交通数据，助力企业拿地决策。`,
      url: `zhaoshang.aitojoy.com/tudi/${urlPath}`
    };
  };

  /**
   * 招商服务-土地详情页
   * @param landName 土地名称
   * @param landId 土地ID
   * @returns SEO数据对象
   */
  static zhaoshangLandDetailPage = (
    landName: string,
    landId: string
  ) => {
    return {
      title: `${landName}可查看土地规划图、投资要求、价格等信息 - 企座`,
      keywords: [
        `了解${landName}规划图信息`,
        `了解${landName}投资要求信息`,
        `了解${landName}周边配套`
      ],
      description: `${landName}转让，可查看土地规划图、投资要求、价格等信息。`,
      url: `zhaoshang.aitojoy.com/detail/tudi/${landId}`
    };
  };
} 