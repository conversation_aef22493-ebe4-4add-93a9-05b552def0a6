# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Playwright-based end-to-end automation testing framework for the enterprise service platform (aitojoy.com). The core testing scope is the PC client (qizuo_pc), with unique integration of Midscene AI for robust visual element recognition and interaction.

## Key Technologies

- Testing Framework: Playwright
- AI Enhancement: Midscene AI
- Language: TypeScript
- Package Manager: pnpm

## Project Structure

```
midscene-playwright/
├── e2e/                          # End-to-end test directory
│   ├── qizuo_pc/                 # PC client tests
│   │   ├── config/               # Environment configuration
│   │   ├── pages/                # Page Object Model
│   │   ├── tests/                # Test cases
│   │   ├── common/               # Common utilities
│   │   ├── data/                 # Test data
│   │   └── scripts/              # Helper scripts
│   ├── Android/                  # Android client tests
│   └── aitojoy/                  # Other test projects
├── reports/                      # Test reports
├── scripts/                      # Project scripts
├── data/                         # Shared test data
├── playwright.config.ts          # Playwright configuration
└── package.json                  # Project dependencies
```

## Core Commands

### Development Setup
```bash
# Install dependencies
pnpm install

# Update PC authentication information
pnpm run update-auth:pc

# Update admin authentication information
pnpm run update-auth:admin
```

### Running Tests
```bash
# Run all web tests
pnpm run e2e

# Run with cache (faster subsequent runs)
pnpm run e2e:cache

# Start Playwright UI debug mode
pnpm run e2e:ui

# Run Android tests
pnpm run wecom
```

## Architecture Patterns

### Page Object Model (POM)
UI interaction logic is organized using POM, encapsulating page elements and operations in independent classes for maintainability and reusability.

### AI-Driven Testing
The project leverages Midscene AI for enhanced test capabilities:
- `aiTap`: AI-assisted visual clicking
- `aiInput`: AI-assisted visual input
- `aiAssert`: AI-assisted visual assertion
- `aiQuery`: AI-assisted visual querying
- `aiWaitFor`: AI-assisted waiting for visual conditions

## Environment Configuration
Environment variables are managed through a centralized configuration system:
- Use `.env` file for configuration (copy from `.env.example`)
- Supports test and production environments via `QIZUO_ENV` variable
- Automatic URL generation for subdomains based on environment

## Test Data Management
SEO test data is organized in the `e2e/qizuo_pc/data/seo-data/` directory with separate files for different page types (e.g., industry, activity, policy, etc.).

## Authentication
Authentication state is managed through JSON files in the `data/` directory, automatically loaded based on the current environment setting.

## Reporting
The framework generates two types of reports:
1. Midscene Report: AI operation analysis
2. Playwright Report: Detailed execution steps with traces and screenshots