/**
 * 创业课堂SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class KetangSEOData extends BaseSEOData {
  // 创业课堂-频道页
  static courseChannelPage: SEOTestDataItem = {
    title: "全岗位培训课程 企业家创业干货分享 - 企座",
    keywords: [
      "企业员工学习提高平台",
      "管理岗成长课程",
      "企业一线员工提高课程",
      "企业培训SaaS系统"
    ],
    description: "企座创业课堂覆盖战略管理、专业技能、职业素养的体系化培训课程，支持在线学习，配套企业培训管理SaaS系统，可实现课程定制、学习追踪、测验考试等丰富功能。",
    url: "ketang.aitojoy.com"
  };

  // 创业课堂-列表页
  static courseListPage: SEOTestDataItem = {
    title: "企业培训专题课程 - 企座",
    keywords: [
      "领导力课程",
      "高管培训课程",
      "员工技能培训",
      "销售技巧学习",
      "财务管理课程",
      "人力资源课程"
    ],
    description: "企座创业课堂提供超过20个不同类型课程，适用于企业20余种不同岗位，支持在线学习。提升员工能力与企业竞争力。",
    url: "ketang.aitojoy.com/list"
  };

  /**
   * 创业课堂-列表页-课程分类筛选
   * @param category 课程分类
   * @param categoryPinyin 课程分类筛选项全拼
   * @returns SEO数据对象
   */
  static courseListPageCategoryFilter = (category: string, categoryPinyin: string) => {
    return {
      title: `${category} 精选课程 - 企座`,
      keywords: [
        `${category}精选课程`,
        "员工技能培训",
        "职场能力提升"
      ],
      description: `企座为您提供${category}精选课程,支持在线学习。提升员工能力与企业竞争力。`,
      url: `ketang.aitojoy.com/list/fenlei-${categoryPinyin}`
    };
  };

  /**
   * 创业课堂-列表页-课程标签筛选
   * @param tag 课程标签
   * @param tagPinyin 课程标签筛选项全拼
   * @returns SEO数据对象
   */
  static courseListPageTagFilter = (tag: string, tagPinyin: string) => {
    return {
      title: `${tag} 精选课程 - 企座`,
      keywords: [
        `${tag}精选课程`,
        "员工技能培训",
        "职场能力提升"
      ],
      description: `企座为您提供${tag}精选课程,支持在线学习。提升员工能力与企业竞争力。`,
      url: `ketang.aitojoy.com/list/biaoqian-${tagPinyin}`
    };
  };

  /**
   * 创业课堂-列表页-适合岗位筛选
   * @param position 适合岗位
   * @param positionPinyin 适合岗位筛选项全拼
   * @returns SEO数据对象
   */
  static courseListPagePositionFilter = (position: string, positionPinyin: string) => {
    return {
      title: `${position} 精选课程 - 企座`,
      keywords: [
        `${position}精选课程`,
        "员工技能培训",
        "职场能力提升"
      ],
      description: `企座为您提供${position}精选课程,支持在线学习。提升员工能力与企业竞争力。`,
      url: `ketang.aitojoy.com/list/gangwei-${positionPinyin}`
    };
  };

  /**
   * 创业课堂-详情页
   * @param courseName 课程名称
   * @param courseId 课程ID
   * @param position 适合岗位
   * @param category 课程分类
   * @param tag 课程标签
   * @param courseIntro 课程简介
   * @returns SEO数据对象
   */
  static courseDetailPage = (
    courseName: string,
    courseId: string,
    position: string = '',
    category: string = '',
    tag: string = '',
    courseIntro: string = ''
  ) => {
    return {
      title: `${position}精选课程${courseName} - 企座`,
      keywords: [
        `${position}精选课程${courseName}`,
        `${category}精选课程${courseName}`,
        `${tag}精选课程${courseName}`
      ],
      description: courseIntro || `${courseName}课程简介`,
      url: `ketang.aitojoy.com/detail/${courseId}`
    };
  };

  // 创业课堂-成长班列表页
  static courseGrowthClassListPage: SEOTestDataItem = {
    title: "岗位胜任成长班 从管理到一线各类岗位课程 支持在线学习 - 企座",
    keywords: [
      "岗位胜任成长班",
      "管理岗位技能学习",
      "各岗位专业能力提升"
    ],
    description: "企座岗位胜任成长班，为企业不同岗位人才提供体系化学习课程，助您轻松胜任。选择课程即可开始在线学习。",
    url: "ketang.aitojoy.com/chengzhangban"
  };

  /**
   * 创业课堂-成长班详情页
   * @param growthClassName 成长班名称
   * @param growthClassId 成长班ID
   * @returns SEO数据对象
   */
  static courseGrowthClassDetailPage = (
    growthClassName: string,
    growthClassId: string
  ) => {
    return {
      title: `${growthClassName}全部课程 - 企座`,
      keywords: [
        `${growthClassName}全部课程`
      ],
      description: `${growthClassName}，为您的岗位定制体系化学习课程，助您轻松胜任。`,
      url: `ketang.aitojoy.com/chengzhangban/${growthClassId}`
    };
  };
} 