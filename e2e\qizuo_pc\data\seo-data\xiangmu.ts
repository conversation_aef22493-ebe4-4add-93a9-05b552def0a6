/**
 * 找项目SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class XiangmuSEOData extends BaseSEOData {
  // 找项目-列表页
  static projectListPage: SEOTestDataItem = {
    title: "找项目 找合作 找合伙 - 企座",
    keywords: [
      "创业项目孵化",
      "创新项目合作联营",
      "项目详细介绍",
      "项目企业信息",
      "项目AI解读",
      "项目产业链信息"
    ],
    description: "企座平台提供50万+创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。支持有合作意向客户在线对接项目合作联营。",
    url: "xiangmu.aitojoy.com"
  };

  // 找项目-列表页-行业筛选
  static projectListPageIndustryFilter = (industry: string | string[]) => {
    // 处理单个行业或多个行业
    const industryStr = Array.isArray(industry) ? industry.join('') : industry;
    
    // 将中文行业名称转换为拼音
    const industryPinyin = BaseSEOData.chineseToPinyin(industry);
    
    // 处理URL部分，如果是数组则用逗号连接
    const industryUrlPart = Array.isArray(industryPinyin) ? industryPinyin.join(',') : industryPinyin;

    return {
      title: `${industryStr}的优质项目 - 企座`,
      keywords: [
        `${industryStr}创业项目孵化`,
        `${industryStr}创新项目合作联营`,
        `${industryStr}项目详细介绍`,
        `${industryStr}项目企业信息`,
        `${industryStr}项目AI解读`,
        `${industryStr}项目产业链信息`
      ],
      description: `企座平台提供${industryStr}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。支持有合作意向客户在线对接项目合作联营。`,
      url: `xiangmu.aitojoy.com/hangye-${industryUrlPart}`
    };
  };

  // 找项目-列表页-行业筛选 (用于测试实际URL)
  static projectListPageIndustryFilterForTest = (industry: string | string[]) => {
    // 处理单个行业或多个行业
    const industryStr = Array.isArray(industry) ? industry.join('') : industry;
    
    // 将中文行业名称转换为拼音
    const industryPinyin = BaseSEOData.chineseToPinyin(industry);
    
    // 处理URL部分，如果是数组则用逗号连接
    const industryUrlPart = Array.isArray(industryPinyin) ? industryPinyin.join(',') : industryPinyin;

    return {
      title: `${industryStr}的优质项目 - 企座`,
      keywords: [
        `${industryStr}创业项目孵化`,
        `${industryStr}创新项目合作联营`,
        `${industryStr}项目详细介绍`,
        `${industryStr}项目企业信息`,
        `${industryStr}项目AI解读`,
        `${industryStr}项目产业链信息`
      ],
      description: `企座平台提供${industryStr}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。支持有合作意向客户在线对接项目合作联营。`,
      url: `hangye-${industryUrlPart}`
    };
  };

  // 找项目-列表页-国家地区筛选
  static projectListPageRegionFilter = (region: string | string[]) => {
    // 处理单个地区或省市二级地区
    const isArray = Array.isArray(region);
    const regionStr = isArray ? region.join('') : region;

    // 生成URL部分
    let urlPart = "";
    if (isArray) {
      // 省市二级地区，例如：["浙江省", "杭州市"] => "zhejiangsheng-hangzhoushi"
      // 使用拼音工具类将中文转换为拼音
      const pinyinArray = BaseSEOData.chineseToPinyin(region);
      if (Array.isArray(pinyinArray)) {
        urlPart = pinyinArray.join('-');
      }
    } else {
      // 单个地区
      const pinyin = BaseSEOData.chineseToPinyin(region);
      if (typeof pinyin === 'string') {
        urlPart = pinyin;
      }
    }

    return {
      title: `${regionStr}的优质项目 - 企座`,
      keywords: [
        `${regionStr}创业项目孵化`,
        `${regionStr}创新项目合作联营`,
        `${regionStr}项目详细介绍`,
        `${regionStr}项目企业信息`,
        `${regionStr}项目AI解读`,
        `${regionStr}项目产业链信息`
      ],
      description: `企座平台提供${regionStr}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。支持有合作意向客户在线对接项目合作联营。`,
      url: `xiangmu.aitojoy.com/diqu-${urlPart}`
    };
  };

  // 找项目-列表页-合作金额筛选
  static projectListPageAmountFilter = (amountRange: string) => ({
    title: `合作金额${amountRange}的优质项目`,
    keywords: [
      `${amountRange}创业项目孵化`,
      `${amountRange}创新项目合作联营`,
      `${amountRange}项目详细介绍`,
      `${amountRange}项目企业信息`,
      `${amountRange}项目AI解读`,
      `${amountRange}项目产业链信息`
    ],
    description: `企座平台提供${amountRange}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。同时支持有合作意向客户在线对接项目合作联营。`,
    url: "xiangmu.aitojoy.com/jine-"
  });

  /**
   * 组合筛选的SEO数据生成
   * @param region 地区，可以是省份或城市
   * @param industry 行业，可以是单个行业或行业数组
   * @param amountRange 金额范围
   * @param generateUrl 是否生成完整URL，默认为true
   * @returns SEO数据对象
   */
  static projectListPageCombinedFilter = (
    region: string | string[] = '',
    industry: string | string[] = '',
    amountRange: string = '',
    generateUrl: boolean = true
  ) => {
    // 处理地区
    const regionStr = Array.isArray(region) ? region.join('') : region;

    // 处理行业
    const industryStr = Array.isArray(industry) ? industry.join('') : industry;

    // 生成标题，如果所有参数都为空，则使用"优质项目"
    const title = `${regionStr}${industryStr}${amountRange}的优质项目`.replace(/^的优质项目$/, '优质项目');

    // 生成关键词
    const keywords = [
      `${regionStr}${industryStr}${amountRange}创业项目孵化`,
      `${regionStr}${industryStr}${amountRange}创新项目合作联营`,
      `${regionStr}${industryStr}${amountRange}项目详细介绍`,
      `${regionStr}${industryStr}${amountRange}项目企业信息`,
      `${regionStr}${industryStr}${amountRange}项目AI解读`,
      `${regionStr}${industryStr}${amountRange}项目产业链信息`
    ];

    // 生成描述
    const description = `企座平台提供${regionStr}${industryStr}${amountRange}创新项目筛选查询，支持查看项目详细介绍、经营信息、关联产业链、所属企业信息等多维度信息。同时支持有合作意向客户在线对接项目合作联营。`;

    // 生成URL
    let url = "xiangmu.aitojoy.com";

    if (generateUrl) {
      // 构建URL部分
      const urlParts: string[] = [];

      // 添加行业部分
      if (industry && industry.length > 0) {
        // 将中文行业名称转换为拼音
        const industryPinyin = BaseSEOData.chineseToPinyin(industry);
        const industryPart = Array.isArray(industryPinyin)
          ? industryPinyin.join(',')
          : industryPinyin;
        urlParts.push(`hangye-${industryPart}`);
      }

      // 添加地区部分
      if (region && region.length > 0) {
        // 将中文地区名称转换为拼音
        const regionPinyin = BaseSEOData.chineseToPinyin(region);
        const regionPart = Array.isArray(regionPinyin)
          ? regionPinyin.join('-')
          : regionPinyin;
        urlParts.push(`diqu-${regionPart}`);
      }

      // 添加金额部分
      if (amountRange) {
        // 假设金额格式为"30万-50万"，需要转换为"30wanzhi50wan"
        const formattedAmount = amountRange.replace('-', 'zhi');
        urlParts.push(`jine-${formattedAmount}`);
      }

      // 组合URL
      if (urlParts.length > 0) {
        url = `xiangmu.aitojoy.com/${urlParts.join('/')}`;
      }
    }

    return {
      title,
      keywords,
      description,
      url
    };
  };

  /**
   * 项目详情页SEO数据生成
   * @param projectName 项目名称
   * @param projectId 项目ID
   * @param region 项目所属地区
   * @param industry 项目所属行业
   * @param amountRange 合作金额区间
   * @returns SEO数据对象
   */
  static projectDetailPage = (
    projectName: string,
    projectId: string,
    region: string = '',
    industry: string = '',
    amountRange: string = ''
  ) => {
    return {
      title: `${region}${industry}${amountRange}的优质项目${projectName}`,
      keywords: [
        `${projectName}详细介绍`,
        `${projectName}企业信息`,
        `${projectName}AI解读`,
        `${projectName}产业链信息`
      ],
      description: `企座为您提供${projectName}详细介绍、企业信息、AI解读、产业链信息等多维度信息`,
      url: `xiangmu.aitojoy.com/detail/${projectId}`
    };
  };
} 