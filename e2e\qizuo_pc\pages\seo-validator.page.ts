// @ts-check
import type { Page } from '@playwright/test';
import { expect } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';

/**
 * SEO验证页面对象
 * 提供验证网页Title、Keywords、Description和URL的方法
 * 支持使用MidsceneJS的AI驱动定位
 */
export class SEOValidatorPage {
  private readonly page: Page;
  private readonly ai: PlayWrightAiFixtureType['ai'];
  private readonly aiQuery: PlayWrightAiFixtureType['aiQuery'];
  private readonly aiAssert: PlayWrightAiFixtureType['aiAssert'];

  constructor(page: Page, aiFixtures?: PlayWrightAiFixtureType) {
    this.page = page;
    if (aiFixtures) {
      this.ai = aiFixtures.ai;
      this.aiQuery = aiFixtures.aiQuery;
      this.aiAssert = aiFixtures.aiAssert;
    }
  }

  /**
   * 验证页面标题
   * @param expectedTitle 预期的页面标题
   */
  async validateTitle(expectedTitle: string): Promise<void> {
    const title = await this.page.title();
    expect(title, `页面标题应为"${expectedTitle}"`).toBe(expectedTitle);
    console.log(`✅ 标题验证通过: "${title}"`);
  }

  /**
   * 验证页面关键词
   * @param expectedKeywords 预期的关键词数组
   */
  async validateKeywords(expectedKeywords: string[]): Promise<void> {
    // 获取meta keywords内容
    const keywords = await this.page.locator('meta[name="keywords"]').getAttribute('content');

    // 验证keywords元素存在
    expect(keywords, 'meta keywords元素应存在').not.toBeNull();

    if (keywords) {
      // 验证每个预期的关键词都包含在页面关键词中
      for (const keyword of expectedKeywords) {
        expect(keywords.toLowerCase(), `关键词应包含"${keyword}"`).toContain(keyword.toLowerCase());
      }
      console.log(`✅ 关键词验证通过: "${keywords}"`);
    }
  }

  /**
   * 验证页面描述
   * @param expectedDescription 预期的页面描述
   */
  async validateDescription(expectedDescription: string): Promise<void> {
    // 获取meta description内容
    const description = await this.page.locator('meta[name="description"]').getAttribute('content');

    // 验证description元素存在
    expect(description, 'meta description元素应存在').not.toBeNull();

    if (description) {
      // 使用宽容验证，检查描述是否包含预期描述的关键部分
      const descriptionWords = expectedDescription.split(' ').filter(word => word.length > 3);
      const significantWords = descriptionWords.filter(word => word.length > 5).slice(0, 5);

      for (const word of significantWords) {
        expect(description.toLowerCase(), `描述应包含关键词"${word}"`).toContain(word.toLowerCase());
      }

      console.log(`✅ 描述验证通过: "${description}"`);
    }
  }

  /**
   * 验证页面URL
   * @param expectedURL 预期的URL（可以是部分URL）
   */
  async validateURL(expectedURL: string): Promise<void> {
    const currentURL = this.page.url();

    // 使用宽容验证，检查当前URL是否包含预期URL
    expect(currentURL.toLowerCase(), `URL应包含"${expectedURL}"`).toContain(expectedURL.toLowerCase());
    console.log(`✅ URL验证通过: "${currentURL}"`);
  }

  /**
   * 使用软断言验证所有SEO元素（TDK）
   * 即使某个断言失败，也会继续验证其他元素
   * @param testData 包含title、keywords、description和url的测试数据对象
   * @param page 可选，指定要操作的页面对象，默认this.page
   * @returns 验证结果，包含通过和失败的项
   */
  async validateTDKWithSoftAssertions(testData: {
    title: string,
    keywords: string[],
    description: string,
    url: string
  }, page?: Page): Promise<{
    success: boolean,
    errors: string[],
    passed: string[],
    actualElements: {title: string, keywords: string | null, description: string | null, url: string},
    expectedElements: {title: string, keywords: string[], description: string, url: string}
  }> {
    const errors: string[] = [];
    const passed: string[] = [];
    const targetPage = page || this.page;
    const seoElements = await this.getSEOElements.call({ ...this, page: targetPage });

    // 验证标题
    if (seoElements.title === testData.title) {
      passed.push(`标题验证通过: "${seoElements.title}"`);
    } else {
      errors.push(`标题验证失败:\n期望: "${testData.title}"\n实际: "${seoElements.title}"`);
    }

    // 验证关键词
    if (seoElements.keywords) {
      let keywordsPass = true;
      const missingKeywords: string[] = [];
      for (const keyword of testData.keywords) {
        if (!seoElements.keywords.toLowerCase().includes(keyword.toLowerCase())) {
          keywordsPass = false;
          missingKeywords.push(`"${keyword}"`);
        }
      }
      if (keywordsPass) {
        passed.push(`关键词验证通过: "${seoElements.keywords}"`);
      } else {
        errors.push(`\n关键词验证失败:\n缺少: ${missingKeywords.join(', ')}\n实际keywords为: "${seoElements.keywords}"`);
      }
    } else {
      errors.push('关键词验证失败: meta keywords元素不存在');
    }

    // 验证描述
    if (seoElements.description === testData.description) {
      passed.push(`描述验证通过: "${seoElements.description}"`);
    } else if (seoElements.description) {
      errors.push(`\n描述验证失败:\n期望: "${testData.description}"\n实际: "${seoElements.description}"`);
    } else {
      errors.push('描述验证失败: meta description元素不存在');
    }

    // 验证URL
    if (targetPage.url().toLowerCase().includes(testData.url.toLowerCase())) {
      passed.push(`URL验证通过: "${targetPage.url()}"`);
    } else {
      errors.push(`\nURL验证失败:\n期望包含: "${testData.url}"\n实际: "${targetPage.url()}"`);
    }

    // 输出验证结果
    console.log('\n===== SEO验证结果 =====');
    if (passed.length > 0) {
      console.log('✅ 通过的验证:');
      passed.forEach(item => console.log(`  ${item}`));
    }

    if (errors.length > 0) {
      console.log('❌ 失败的验证:');
      errors.forEach(item => console.log(`  ${item}`));
    }
    console.log('========================\n');

    return {
      success: errors.length === 0,
      errors,
      passed,
      actualElements: seoElements,
      expectedElements: testData
    };
  }


  /**
   * 获取页面的所有SEO元素
   * 返回标题、关键词、描述和URL
   * 支持使用AI查询或传统定位方式
   */
  async getSEOElements(): Promise<{title: string, keywords: string | null, description: string | null, url: string}> {
    if (this.aiQuery) {
      try {
        // 使用AI查询获取SEO元素
        const seoData = await this.aiQuery<{
          title: string,
          keywords: string | null,
          description: string | null,
          url: string
        }>('获取页面的SEO元素，包括标题、meta keywords内容、meta description内容和当前URL');

        return {
          title: seoData.title || await this.page.title(),
          keywords: seoData.keywords,
          description: seoData.description,
          url: seoData.url || this.page.url()
        };
      } catch (error) {
        console.warn('AI查询SEO元素失败，回退到传统方式:', error);
        // 如果AI查询失败，回退到传统方式
      }
    }

    // 传统方式获取SEO元素
    const title = await this.page.title();
    const keywords = await this.page.locator('meta[name="keywords"]').getAttribute('content');
    const description = await this.page.locator('meta[name="description"]').getAttribute('content');
    const url = this.page.url();

    return {
      title,
      keywords,
      description,
      url
    };
  }

  /**
   * 生成SEO验证报告
   */
  async generateSEOReport(): Promise<string> {
    const seoElements = await this.getSEOElements();

    return `
SEO验证报告
===========
URL: ${seoElements.url}
标题: ${seoElements.title}
关键词: ${seoElements.keywords || '未设置'}
描述: ${seoElements.description || '未设置'}
===========
    `;
  }
}
