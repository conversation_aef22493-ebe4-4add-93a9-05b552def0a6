/**
 * 起点阅读自动化测试脚本
 *
 * 该脚本自动执行起点阅读App的日常任务，包括：
 * 1. 领取福利和签到
 * 2. 参与抽奖活动
 * 3. 观看激励视频获取奖励
 * 4. 周日兑换章节卡
 */

import { test, expect } from "./fixture";
import { QidianReaderTester } from './agents/qidian-reader-tester';
import { log } from './utils/common';
import { setupExitHandlers, shouldExit, cleanupAndExit } from './utils/exit-handler';

// 设置退出处理程序
setupExitHandlers();

test.describe('起点阅读自动化测试', () => {
  test('执行起点阅读日常任务', async ({ agent }) => {
    log('开始起点阅读自动化任务...', 'info');
    
    try {
      // 创建起点阅读测试代理实例
      const qidianTester = new QidianReaderTester(agent);
      
      // 执行起点阅读自动化流程
      await qidianTester.launchApp();
      
      // 验证应用已成功启动
      const appLaunched = await agent.aiQuery('页面是否已成功加载起点阅读应用');
      expect(appLaunched).toBeTruthy();
      
      await qidianTester.navigateToRewardPage();
      await qidianTester.handleLotteryTask();
      await qidianTester.handleVideoRewardTasks();
      await qidianTester.handleReadingPointsTask();
      
      log('所有起点阅读任务已成功完成！', 'success');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      log(`测试执行过程中发生错误: ${errorMessage}`, 'error');
      console.error(error);
      
      // 在测试框架中，使用fail来标记测试失败
      test.fail(true, `测试执行失败: ${errorMessage}`);
    }
  });
});