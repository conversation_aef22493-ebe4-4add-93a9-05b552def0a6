/**
 * 拼音工具函数
 * 用于将中文转换为拼音
 */
import { pinyin } from 'pinyin-pro';

/**
 * 将中文转换为拼音
 * @param chinese 中文字符串或字符串数组
 * @returns 拼音字符串或字符串数组 (无声调，小写，无空格)
 */
export function chineseToPinyin(chinese: string | string[]): string | string[] {
  const options = { toneType: 'none', type: 'string', nonZh: 'consecutive' } as const;
  if (Array.isArray(chinese)) {
    return chinese.map(item => pinyin(item, options).replace(/\s+/g, ''));
  }
  return pinyin(chinese, options).replace(/\s+/g, '');
} 