/**
 * 企业微信 AI 工作台自动化测试脚本
 * 
 * 该脚本自动执行以下测试流程：
 * 1. 打开企业微信并登录
 * 2. 进入 AI 工作台
 * 3. 测试 AI 工作台基本功能
 * 4. 进入并测试"扣子"智能体各项功能
 * 5. 返回创新社区
 */

// 使用集中环境配置模块，不再直接导入和配置dotenv
import '../../config/env';
import { log } from './utils/common';
import { setupExitHandlers, shouldExit, cleanupAndExit } from './utils/exit-handler';
import { DeviceManager } from './utils/device-manager';
import { WecomAIWorkspaceTester } from './agents/wecom-ai-workspace-tester';
import { KouziAgentTester } from './agents/kouzi-agent-tester';

// 设置退出处理程序
setupExitHandlers();

Promise.resolve(
  (async () => {
    // 创建设备管理器实例
    const deviceManager = new DeviceManager();
    
    try {
      log('开始企业微信AI工作台自动化测试...', 'info');
      
      // 连接设备并创建代理
      const agent = await deviceManager.initAndConnect(
        '如果出现位置、权限、用户协议等弹窗，点击同意。如果出现登录页面，等待扫码。如果出现更新提示，选择稍后或取消。'
      );
      
      // 创建测试代理实例
      const wecomTester = new WecomAIWorkspaceTester(agent);
      const kouziTester = new KouziAgentTester(agent);
      
      // --- 测试流程开始 ---
      
      // 执行企业微信AI工作台测试流程
      await wecomTester.runFullTest();
      
      // 执行"扣子"智能体测试流程
      await kouziTester.runFullTest();
      
      // 返回到创新社区
      await kouziTester.returnToCommunity();
      
      log('所有测试已成功完成！', 'success');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      log(`测试执行过程中发生错误: ${errorMessage}`, 'error');
      console.error(error);
      throw error; // 重新抛出错误，以便测试运行器能捕获到失败状态
    } finally {
      // 确保资源被释放
      try {
        await deviceManager.cleanup();
      } catch (cleanupError) {
        const errorMessage = cleanupError instanceof Error ? cleanupError.message : String(cleanupError);
        log(`释放设备资源时出错: ${errorMessage}`, 'error');
      }
      
      // 如果是因SIGINT中断而退出
      if (shouldExit) {
        cleanupAndExit('程序因SIGINT优雅退出');
      }
    }
  })()
);
