// @ts-check
import type { Page } from '@playwright/test';
import { expect } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';

export class ZhaoXiangMuPage {
  private readonly page: Page;
  private readonly ai: PlayWrightAiFixtureType['ai'];
  private readonly aiTap: PlayWrightAiFixtureType['aiTap'];
  private readonly aiInput: PlayWrightAiFixtureType['aiInput'];
  private readonly aiAssert: PlayWrightAiFixtureType['aiAssert'];
  private readonly aiHover: PlayWrightAiFixtureType['aiHover'];
  private readonly aiWaitFor: PlayWrightAiFixtureType['aiWaitFor'];

  constructor(page: Page, aiFixtures?: PlayWrightAiFixtureType) {
    this.page = page;
    if (aiFixtures) {
      this.ai = aiFixtures.ai;
      this.aiTap = aiFixtures.aiTap;
      this.aiInput = aiFixtures.aiInput;
      this.aiAssert = aiFixtures.aiAssert;
      this.aiHover = aiFixtures.aiHover;
      this.aiWaitFor = aiFixtures.aiWaitFor;
    }
  }

  /**
   * 导航到找项目页面
   */
  async daohangDaoZhaoXiangMu() {
    if (this.aiTap) {
      // 使用AI点击导航栏中的找项目链接
      await this.aiTap('导航栏中的"找项目"链接');
    } else {
      await this.page.getByRole('link', { name: '找项目' }).click();
    }

    // 等待页面加载
    if (this.aiWaitFor) {
      await this.aiWaitFor('找项目页面已完全加载', { timeoutMs: 5000 });
    } else {
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * 搜索项目
   */
  async sousuoXiangMu(keyword: string) {
    if (this.aiInput) {
      // 使用AI在搜索框中输入关键词
      await this.aiInput(keyword, '找项目搜索框');
      
      // 点击搜索按钮
      await this.aiTap('搜索按钮');
    } else {
      await this.page.getByPlaceholder('请输入感兴趣的项目').fill(keyword);
      await this.page.getByRole('button', { name: '搜索' }).click();
    }
    
    // 等待搜索结果加载
    if (this.aiWaitFor) {
      await this.aiWaitFor('搜索结果已加载', { timeoutMs: 5000 });
    } else {
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * 选择行业分类
   */
  async xuanzeHangyeFenlei(category: string) {
    if (this.aiTap) {
      // 使用AI点击指定的行业分类
      await this.aiTap(`行业分类中的"${category}"`);
    } else {
      // 定位行业分类区域并点击指定分类
      await this.page.locator('div').filter({ hasText: /^行业分类/ }).getByText(category).click();
    }
    
    // 等待筛选结果更新
    if (this.aiWaitFor) {
      await this.aiWaitFor('筛选结果已更新', { timeoutMs: 3000 });
    } else {
      await this.page.waitForTimeout(1000); // 等待筛选结果更新
    }
  }

  /**
   * 选择国家地区
   */
  async xuanzeDiqu(region: string) {
    if (this.aiTap) {
      // 使用AI点击指定的地区
      await this.aiTap(`国家地区中的"${region}"`);
    } else {
      // 定位国家地区区域并点击指定地区
      await this.page.locator('div').filter({ hasText: /^国家地区/ }).getByText(region).click();
    }
    
    // 等待筛选结果更新
    if (this.aiWaitFor) {
      await this.aiWaitFor('筛选结果已更新', { timeoutMs: 3000 });
    } else {
      await this.page.waitForTimeout(1000); // 等待筛选结果更新
    }
  }

  /**
   * 选择融资轮次
   */
  async xuanzeRongziLunci(round: string) {
    if (this.aiTap) {
      // 使用AI点击指定的融资轮次
      await this.aiTap(`当前轮次中的"${round}"`);
    } else {
      // 定位融资轮次区域并点击指定轮次
      await this.page.locator('div').filter({ hasText: /^当前轮次/ }).getByText(round).click();
    }
    
    // 等待筛选结果更新
    if (this.aiWaitFor) {
      await this.aiWaitFor('筛选结果已更新', { timeoutMs: 3000 });
    } else {
      await this.page.waitForTimeout(1000); // 等待筛选结果更新
    }
  }

  /**
   * 选择合作金额
   */
  async xuanzeHezuoJine(amount: string) {
    if (this.aiTap) {
      // 使用AI点击指定的合作金额
      await this.aiTap(`合作金额中的"${amount}"`);
    } else {
      // 定位合作金额区域并点击指定金额
      await this.page.locator('div').filter({ hasText: /^合作金额/ }).getByText(amount).click();
    }
    
    // 等待筛选结果更新
    if (this.aiWaitFor) {
      await this.aiWaitFor('筛选结果已更新', { timeoutMs: 3000 });
    } else {
      await this.page.waitForTimeout(1000); // 等待筛选结果更新
    }
  }

  /**
   * 验证搜索结果包含特定文本
   */
  async yanzhengSousuoJieguo(expectedText: string) {
    if (this.aiAssert) {
      // 使用AI断言验证搜索结果
      await this.aiAssert(`搜索结果中包含"${expectedText}"`);
    } else {
      await expect(this.page.getByText(expectedText)).toBeVisible();
    }
  }

  /**
   * 验证项目数量
   */
  async yanzhengXiangmuShuliang(expectedCount: number) {
    if (this.aiAssert) {
      // 使用AI断言验证项目数量
      await this.aiAssert(`项目数量为${expectedCount}个`);
    } else {
      // 获取结果数量文本并验证
      const countText = await this.page.locator('共 355759条结果').textContent();
      const count = parseInt(countText?.replace(/[^0-9]/g, '') || '0');
      expect(count).toBeGreaterThan(0);
    }
  }

  /**
   * 点击项目卡片
   */
  async dianjiFenpinKa(projectName: string) {
    if (this.aiTap) {
      // 使用AI点击指定名称的项目卡片
      await this.aiTap(`名称为"${projectName}"的项目卡片`);
    } else {
      // 点击包含项目名称的卡片
      await this.page.getByText(projectName).first().click();
    }
    
    // 等待项目详情页面加载
    if (this.aiWaitFor) {
      await this.aiWaitFor('项目详情页面已加载', { timeoutMs: 5000 });
    } else {
      await this.page.waitForLoadState('networkidle');
    }
  }

  /**
   * 验证项目列表可见性
   */
  async yanzhengXiangmuLiebiaoKejian() {
    if (this.aiAssert) {
      // 使用AI断言验证项目列表是否可见
      await this.aiAssert('项目列表已显示在页面上');
    } else {
      // 验证项目列表容器是否可见
      await expect(this.page.locator('.project-list')).toBeVisible();
    }
  }
  
  /**
   * 点击"我要加盟"按钮
   */
  async dianjiWoyaoJiameng() {
    if (this.aiTap) {
      // 使用AI点击"我要加盟"按钮
      await this.aiTap('我要加盟按钮');
    } else {
      await this.page.getByRole('button', { name: '我要加盟' }).click();
    }
  }
  
  /**
   * 点击"我有项目"按钮
   */
  async dianjiWoyouXiangmu() {
    if (this.aiTap) {
      // 使用AI点击"我有项目"按钮
      await this.aiTap('我有项目按钮');
    } else {
      await this.page.getByRole('button', { name: '我有项目' }).click();
    }
  }
} 