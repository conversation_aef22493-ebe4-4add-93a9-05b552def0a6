// @ts-check
import type { Page } from '@playwright/test';
import { expect } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { BasePage } from './base.page';

export class ZhaoXiangMuPage extends BasePage {
  constructor(page: Page, aiFixtures: PlayWrightAiFixtureType) {
    super(page, aiFixtures);
  }

  /**
   * 导航到找项目页面
   */
  async daohangDaoZhaoXiangMu() {
    // 使用AI点击导航栏中的找项目链接
    await this.aiTap('导航栏中的"找项目"链接');

    // 等待页面加载
    await this.aiWaitFor('找项目页面已完全加载', { timeoutMs: 5000 });
  }

  /**
   * 搜索项目
   */
  async sousuoXiangMu(keyword: string) {
    // 使用AI在搜索框中输入关键词
    await this.aiInput(keyword, '找项目搜索框');
    
    // 点击搜索按钮
    await this.aiTap('搜索按钮');
    
    // 等待搜索结果加载
    await this.aiWaitFor('搜索结果已加载', { timeoutMs: 5000 });
  }

  /**
   * 选择行业分类
   */
  async xuanzeHangyeFenlei(category: string) {
    // 使用AI点击指定的行业分类
    await this.aiTap(`行业分类中的"${category}"`);
    
    // 等待筛选结果更新
    await this.aiWaitFor('筛选结果已更新', { timeoutMs: 3000 });
  }

  /**
   * 选择国家地区
   */
  async xuanzeDiqu(region: string) {
    // 使用AI点击指定的地区
    await this.aiTap(`国家地区中的"${region}"`);
    
    // 等待筛选结果更新
    await this.aiWaitFor('筛选结果已更新', { timeoutMs: 3000 });
  }

  /**
   * 选择融资轮次
   */
  async xuanzeRongziLunci(round: string) {
    // 使用AI点击指定的融资轮次
    await this.aiTap(`当前轮次中的"${round}"`);
    
    // 等待筛选结果更新
    await this.aiWaitFor('筛选结果已更新', { timeoutMs: 3000 });
  }

  /**
   * 选择合作金额
   */
  async xuanzeHezuoJine(amount: string) {
    // 使用AI点击指定的合作金额
    await this.aiTap(`合作金额中的"${amount}"`);
    
    // 等待筛选结果更新
    await this.aiWaitFor('筛选结果已更新', { timeoutMs: 3000 });
  }

  /**
   * 验证搜索结果包含特定文本
   */
  async yanzhengSousuoJieguo(expectedText: string) {
    // 使用AI断言验证搜索结果
    await this.aiAssert(`搜索结果中包含"${expectedText}"`);
  }

  /**
   * 验证项目数量
   */
  async yanzhengXiangmuShuliang(expectedCount: number) {
    // 使用AI断言验证项目数量
    await this.aiAssert(`项目数量为${expectedCount}个`);
  }

  /**
   * 点击项目卡片
   */
  async dianjiFenpinKa(projectName: string) {
    // 使用AI点击指定的项目卡片
    await this.aiTap(`"${projectName}"项目卡片`);
    
    // 等待项目详情页面加载
    await this.aiWaitFor('项目详情页面已加载', { timeoutMs: 5000 });
  }

  /**
   * 验证项目列表可见性
   */
  async yanzhengXiangmuLiebiaoKejian() {
    // 使用AI断言验证项目列表可见
    await this.aiAssert('项目列表已显示在页面上');
  }
  
  /**
   * 点击"我要加盟"按钮
   */
  async dianjiWoyaoJiameng() {
    // 使用AI点击"我要加盟"按钮
    await this.aiTap('我要加盟按钮');
  }
  
  /**
   * 点击"我有项目"按钮
   */
  async dianjiWoyouXiangmu() {
    // 使用AI点击"我有项目"按钮
    await this.aiTap('我有项目按钮');
  }
} 