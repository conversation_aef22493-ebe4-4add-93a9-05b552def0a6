// 更新认证状态脚本
import { chromium } from '@playwright/test';
import path from 'path';
import fs from 'fs';

// 导入环境配置系统
import { envConfig, URL_CONFIG } from '../e2e/qizuo_pc/config/environment.config';

/**
 * 根据环境和类型生成认证文件路径
 * @param type 认证类型 ('pc' | 'admin')
 * @returns 认证文件路径
 */
function getAuthFilePath(type: string): string {
  const env = envConfig.getEnvironment();
  const baseDir = './data';
  
  switch (type.toLowerCase()) {
    case 'pc':
      return `${baseDir}/qizuo-pc-auth-${env}.json`;
    case 'admin':
      return `${baseDir}/qizuo-admin-auth-${env}.json`;
    default:
      return `${baseDir}/qizuo-${type}-auth-${env}.json`;
  }
}

/**
 * 更新认证状态文件
 * @param loginUrl 登录页面URL
 * @param authFilePath 认证状态保存路径
 */
async function updateAuth(loginUrl: string, authFilePath: string) {
  console.log(`开始更新认证状态文件: ${authFilePath}`);
  console.log(`登录URL: ${loginUrl}`);
  
  // 启动浏览器（非无头模式，以便可以看到登录过程）
  const browser = await chromium.launch({ 
    headless: false,
    slowMo: 100 // 放慢操作速度，便于观察
  });
  
  // 创建新的上下文
  const context = await browser.newContext({
    viewport: { width: 1920, height: 1080 }
  });
  
  // 创建新的页面
  const page = await context.newPage();
  
  try {
    // 导航到登录页面
    console.log('正在导航到登录页面...');
    await page.goto(loginUrl);
    
    // 等待用户手动登录
    console.log('\n请在打开的浏览器中完成登录操作...');
    console.log('登录成功后，脚本将自动保存认证状态');
    console.log('或者，登录完成后可以手动关闭浏览器，脚本仍会尝试保存认证状态\n');
    
    try {
      // 等待用户登录完成
      await page.waitForTimeout(60000); // 给用户60秒时间登录
    } catch (timeoutError) {
      // 如果用户手动关闭了浏览器，会在这里捕获错误
      console.log('检测到浏览器已关闭，尝试保存登录状态...');
    }
    
    // 确保目录存在
    const dir = path.dirname(authFilePath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    // 尝试保存认证状态到文件
    try {
      await context.storageState({ path: authFilePath });
      console.log(`\n✅ 认证状态已成功保存到: ${authFilePath}`);
    } catch (saveError) {
      // 如果浏览器已关闭，保存可能会失败
      console.error('无法保存认证状态，浏览器可能已关闭:', saveError);
    }
  } catch (error) {
    console.error('更新认证状态时出错:', error);
  } finally {
    try {
      // 尝试关闭浏览器（如果还没关闭）
      await browser.close();
    } catch (closeError) {
      // 浏览器可能已经被用户手动关闭，忽略错误
    }
  }
}

// 主函数
async function main() {
  // 显示当前环境信息
  console.log('🚀 认证状态更新脚本');
  envConfig.printConfig();

  // 打印环境变量调试信息
  console.log('\n=== 环境变量调试信息 ===');
  console.log(`process.env.QIZUO_ENV = "${process.env.QIZUO_ENV}"`);
  console.log(`process.env.NODE_ENV = "${process.env.NODE_ENV}"`);
  console.log(`envConfig.getEnvironment() = "${envConfig.getEnvironment()}"`);
  console.log('========================\n');

  // 检查命令行参数
  const args = process.argv.slice(2);
  const type = args[0] || 'pc'; // 默认更新PC端认证

  switch (type.toLowerCase()) {
    case 'pc':
      // 更新企座PC端认证 - 使用动态环境配置
      console.log(`\n📱 更新企座PC端认证 (${envConfig.getEnvironment()} 环境)`);
      await updateAuth(
        URL_CONFIG.main,
        getAuthFilePath('pc')
      );
      break;

    case 'admin':
      // 更新后台管理认证 - 保持原有配置
      console.log(`\n🔧 更新后台管理认证 (${envConfig.getEnvironment()} 环境)`);
      await updateAuth(
        process.env.HOUTAI_URL || 'https://admin.aitojoy.com',
        getAuthFilePath('admin')
      );
      break;

    default:
      console.error(`❌ 未知的认证类型: ${type}`);
      console.log('📋 可用选项:');
      console.log('  pc    - 更新企座PC端认证 (支持环境切换)');
      console.log('  admin - 更新后台管理认证');
      console.log('\n💡 使用方法:');
      console.log('  npm run update-auth pc    # 更新PC端认证');
      console.log('  npm run update-auth admin # 更新后台认证');
      console.log('\n🔄 环境切换:');
      console.log('  修改 .env 文件中的 QIZUO_ENV 来切换测试/生产环境');
      console.log('  认证文件将保存为: data/qizuo-[类型]-auth-[环境].json');
      process.exit(1);
  }
}

// 执行主函数
main().catch(console.error);
