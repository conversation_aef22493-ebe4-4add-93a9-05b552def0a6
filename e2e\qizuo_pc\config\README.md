# 环境配置系统

## 快速开始

通过环境变量 `QIZUO_ENV` 控制测试/生产环境切换：

```bash
# .env 文件中设置
QIZUO_ENV=test          # 测试环境（默认）
QIZUO_ENV=production    # 生产环境
```

## 使用方法

```typescript
import { URL_CONFIG } from './config/environment.config';

// 使用预定义的URL
await page.goto(URL_CONFIG.main);        // 主站
await page.goto(URL_CONFIG.search);      // 搜索页
await page.goto(URL_CONFIG.project);     // 找项目
```

## URL 映射规则

| 环境 | 规则 | 示例 |
|------|------|------|
| 测试环境 | `https://test-{subdomain}.aitojoy.com` | `https://test-www.aitojoy.com` |
| 生产环境 | `https://{subdomain}.aitojoy.com` | `https://www.aitojoy.com` |

## 添加新子域名

在 `environment.config.ts` 的 `getUrlConfig()` 方法中添加一行：

```typescript
newService: this.generateUrl('newservice'),
```
