/**
 * SEO测试数据生成工具
 * 为保持向后兼容性，所有模块的数据方法都在此统一导出
 */
import { ShouyeSEOData } from './seo-data/shouye';
import { SousuoSEOData } from './seo-data/sousuo';
import { XiangmuSEOData } from './seo-data/xiangmu';
import { RenmaiSEOData } from './seo-data/renmai';
import { HuodongSEOData } from './seo-data/huodong';
import { ChanyeSEOData } from './seo-data/chanye';
import { ZhengceSEOData } from './seo-data/zhengce';
import { KetangSEOData } from './seo-data/ketang';
import { ZixunSEOData } from './seo-data/zixun';
import { ZijinSEOData } from './seo-data/zijin';
import { QiyeSEOData } from './seo-data/qiye';
import { JiandanSEOData } from './seo-data/jiandan';
import { ZhaoshangSEOData } from './seo-data/zhaoshang';

export class SEOTestData {
  // === 首页 ===
  static homePage = ShouyeSEOData.homePage;

  // === 全局搜索页 ===
  static searchPage = SousuoSEOData.searchPage;
  
  // === 找项目 ===
  static projectListPage = XiangmuSEOData.projectListPage;
  static projectListPageIndustryFilter = XiangmuSEOData.projectListPageIndustryFilter;
  static projectListPageIndustryFilterForTest = XiangmuSEOData.projectListPageIndustryFilterForTest;
  static projectListPageRegionFilter = XiangmuSEOData.projectListPageRegionFilter;
  static projectListPageAmountFilter = XiangmuSEOData.projectListPageAmountFilter;
  static projectListPageCombinedFilter = XiangmuSEOData.projectListPageCombinedFilter;
  static projectDetailPage = XiangmuSEOData.projectDetailPage;
  
  // === 人脉广场 ===
  static connectionChannelPage = RenmaiSEOData.connectionChannelPage;
  static connectionListPage = RenmaiSEOData.connectionListPage;
  static connectionDetailPage = RenmaiSEOData.connectionDetailPage;
  
  // === 聚活动 ===
  static activityChannelPage = HuodongSEOData.activityChannelPage;
  static activityListPage = HuodongSEOData.activityListPage;
  static activityListPageLocationFilter = HuodongSEOData.activityListPageLocationFilter;
  static activityDetailPage = HuodongSEOData.activityDetailPage;
  
  // === 产业服务 ===
  static industryServiceChannelPage = ChanyeSEOData.industryServiceChannelPage;
  static industryServiceListPage = ChanyeSEOData.industryServiceListPage;
  static industryServiceListPageRegionFilter = ChanyeSEOData.industryServiceListPageRegionFilter;
  static industryServiceDetailPage = ChanyeSEOData.industryServiceDetailPage;
  
  // === 惠企政策 ===
  static policyChannelPage = ZhengceSEOData.policyChannelPage;
  static policyRewardListPage = ZhengceSEOData.policyRewardListPage;
  static policyRewardListPageRegionFilter = ZhengceSEOData.policyRewardListPageRegionFilter;
  static policyRewardListPageTypeFilter = ZhengceSEOData.policyRewardListPageTypeFilter;
  static policyRewardListPageCombinedFilter = ZhengceSEOData.policyRewardListPageCombinedFilter;
  static policyHotListPage = ZhengceSEOData.policyHotListPage;
  static policyHotListPageRegionFilter = ZhengceSEOData.policyHotListPageRegionFilter;
  static policyHotListPageIndustryFilter = ZhengceSEOData.policyHotListPageIndustryFilter;
  static policyHotListPageTypeFilter = ZhengceSEOData.policyHotListPageTypeFilter;
  static policyHotListPageCombinedFilter = ZhengceSEOData.policyHotListPageCombinedFilter;
  static policyRewardDetailPage = ZhengceSEOData.policyRewardDetailPage;
  static policyHotDetailPage = ZhengceSEOData.policyHotDetailPage;
  
  // === 创业课堂 ===
  static courseChannelPage = KetangSEOData.courseChannelPage;
  static courseListPage = KetangSEOData.courseListPage;
  static courseListPageCategoryFilter = KetangSEOData.courseListPageCategoryFilter;
  static courseListPageTagFilter = KetangSEOData.courseListPageTagFilter;
  static courseListPagePositionFilter = KetangSEOData.courseListPagePositionFilter;
  static courseDetailPage = KetangSEOData.courseDetailPage;
  static courseGrowthClassListPage = KetangSEOData.courseGrowthClassListPage;
  static courseGrowthClassDetailPage = KetangSEOData.courseGrowthClassDetailPage;
  
  // === 产业资讯 ===
  static industryNewsChannelPage = ZixunSEOData.industryNewsChannelPage;
  static industryNews24HListPage = ZixunSEOData.industryNews24HListPage;
  static industryNewsFocusListPage = ZixunSEOData.industryNewsFocusListPage;
  static industryNewsInsightListPage = ZixunSEOData.industryNewsInsightListPage;
  static industryNewsBusinessListPage = ZixunSEOData.industryNewsBusinessListPage;
  static industryNewsDetailPage = ZixunSEOData.industryNewsDetailPage;
  
  // === 资金服务 ===
  static fundingServiceChannelPage = ZijinSEOData.fundingServiceChannelPage;
  static fundingServiceListPage = ZijinSEOData.fundingServiceListPage;
  static fundingServiceDetailPage = ZijinSEOData.fundingServiceDetailPage;
  
  // === 企业服务 ===
  static enterpriseServiceChannelPage = QiyeSEOData.enterpriseServiceChannelPage;
  static enterpriseServiceListPage = QiyeSEOData.enterpriseServiceListPage;
  static enterpriseServiceDetailPage = QiyeSEOData.enterpriseServiceDetailPage;
  
  // === 简单页面 ===
  static enterpriseDiagnosisPage = JiandanSEOData.enterpriseDiagnosisPage;
  static jointOperationServicePage = JiandanSEOData.jointOperationServicePage;
  static aiLandingPage = JiandanSEOData.aiLandingPage;
  
  // === 招商服务 ===
  static zhaoshangServiceChannelPage = ZhaoshangSEOData.zhaoshangServiceChannelPage;
  static zhaoshangIndustrialParkListPage = ZhaoshangSEOData.zhaoshangIndustrialParkListPage;
  static zhaoshangIndustrialParkListPageRegionFilter = ZhaoshangSEOData.zhaoshangIndustrialParkListPageRegionFilter;
  static zhaoshangIndustrialParkListPageIndustryFilter = ZhaoshangSEOData.zhaoshangIndustrialParkListPageIndustryFilter;
  static zhaoshangIndustrialParkListPageCombinedFilter = ZhaoshangSEOData.zhaoshangIndustrialParkListPageCombinedFilter;
  static zhaoshangDevelopmentZoneListPage = ZhaoshangSEOData.zhaoshangDevelopmentZoneListPage;
  static zhaoshangDevelopmentZoneListPageRegionFilter = ZhaoshangSEOData.zhaoshangDevelopmentZoneListPageRegionFilter;
  static zhaoshangDevelopmentZoneListPageIndustryFilter = ZhaoshangSEOData.zhaoshangDevelopmentZoneListPageIndustryFilter;
  static zhaoshangDevelopmentZoneListPageCombinedFilter = ZhaoshangSEOData.zhaoshangDevelopmentZoneListPageCombinedFilter;
  static zhaoshangDevelopmentZoneDetailPage = ZhaoshangSEOData.zhaoshangDevelopmentZoneDetailPage;
  static zhaoshangIndustrialParkDetailPage = ZhaoshangSEOData.zhaoshangIndustrialParkDetailPage;
  
  // === 招商服务-厂房 ===
  static zhaoshangFactoryListPage = ZhaoshangSEOData.zhaoshangFactoryListPage;
  static zhaoshangFactoryListPageRegionFilter = ZhaoshangSEOData.zhaoshangFactoryListPageRegionFilter;
  static zhaoshangFactoryListPageAreaFilter = ZhaoshangSEOData.zhaoshangFactoryListPageAreaFilter;
  static zhaoshangFactoryListPageRentFilter = ZhaoshangSEOData.zhaoshangFactoryListPageRentFilter;
  static zhaoshangFactoryListPageCombinedFilter = ZhaoshangSEOData.zhaoshangFactoryListPageCombinedFilter;
  static zhaoshangFactoryDetailPage = ZhaoshangSEOData.zhaoshangFactoryDetailPage;
  
  // === 招商服务-仓库 ===
  static zhaoshangWarehouseListPage = ZhaoshangSEOData.zhaoshangWarehouseListPage;
  static zhaoshangWarehouseListPageRegionFilter = ZhaoshangSEOData.zhaoshangWarehouseListPageRegionFilter;
  static zhaoshangWarehouseListPageAreaFilter = ZhaoshangSEOData.zhaoshangWarehouseListPageAreaFilter;
  static zhaoshangWarehouseListPageRentFilter = ZhaoshangSEOData.zhaoshangWarehouseListPageRentFilter;
  static zhaoshangWarehouseListPageCombinedFilter = ZhaoshangSEOData.zhaoshangWarehouseListPageCombinedFilter;
  static zhaoshangWarehouseDetailPage = ZhaoshangSEOData.zhaoshangWarehouseDetailPage;
  
  // === 招商服务-土地 ===
  static zhaoshangLandListPage = ZhaoshangSEOData.zhaoshangLandListPage;
  static zhaoshangLandListPageRegionFilter = ZhaoshangSEOData.zhaoshangLandListPageRegionFilter;
  static zhaoshangLandListPageAreaFilter = ZhaoshangSEOData.zhaoshangLandListPageAreaFilter;
  static zhaoshangLandListPagePriceFilter = ZhaoshangSEOData.zhaoshangLandListPagePriceFilter;
  static zhaoshangLandListPageCombinedFilter = ZhaoshangSEOData.zhaoshangLandListPageCombinedFilter;
  static zhaoshangLandDetailPage = ZhaoshangSEOData.zhaoshangLandDetailPage;
}
