# 阿里云模型配置 (已注释)
# OPENAI_API_KEY=your_aliyun_api_key_here
# OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
# MIDSCENE_MODEL_NAME=qvq-max
# MIDSCENE_MODEL_NAME=qvq-72b-preview

# OpenRouter模型配置 (已注释)
# OPENAI_BASE_URL=https://openrouter.ai/api/v1
# OPENAI_API_KEY=your_openrouter_api_key_here
# MIDSCENE_MODEL_NAME=qwen/qwen2.5-vl-72b-instruct:free

# 豆包模型配置 (当前使用)
OPENAI_API_KEY=your_doubao_api_key_here
OPENAI_BASE_URL=https://ark.cn-beijing.volces.com/api/v3
MIDSCENE_MODEL_NAME=doubao-1.5-vision-pro-250328

# 公益站模型配置 (已注释)
# OPENAI_BASE_URL=https://ai.huan666.de/v1
# OPENAI_API_KEY=your_gongyi_api_key_here
# MIDSCENE_MODEL_NAME=qvq-max-2025-05-15

# 谷歌模型配置 (已注释)
# OPENAI_BASE_URL="https://generativelanguage.googleapis.com/v1beta/openai/"
# OPENAI_API_KEY="your_google_api_key_here"
# MIDSCENE_MODEL_NAME="gemini-2.5-pro-preview-06-05"
# MIDSCENE_USE_GEMINI=1

# 视觉模型选择配置
MIDSCENE_USE_GEMINI=false
MIDSCENE_USE_QWEN_VL=false
MIDSCENE_USE_DOUBAO_VISION=true

# 登录凭证
LOGIN_PHONE=your_phone_number_here
LOGIN_PASSWORD=your_password_here

# 环境配置
HOUTAI_URL=your_admin_url_here
QIZUO_PC_URL=your_pc_url_here
QIZUO_ENV=test_or_production