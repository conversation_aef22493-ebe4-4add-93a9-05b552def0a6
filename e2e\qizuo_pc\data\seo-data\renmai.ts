/**
 * 人脉广场SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class RenmaiSEOData extends BaseSEOData {
  // 人脉广场-频道页
  static connectionChannelPage: SEOTestDataItem = {
    title: "创业人脉智能推荐 精准对接合作伙伴、投资人等各类企业家人脉 -企座",
    keywords: [
      "合作伙伴推荐",
      "管理合伙人推荐",
      "投资人推荐",
      "知名企业家",
      "AI人脉推荐"
    ],
    description: "企座人脉广场通过大数据+AI大模型算法，为创业者与企业家智能推荐合作伙伴、管理合伙人、投资人各类优质人脉。注册完善基本信息后，即可使用智能推荐服务！",
    url: "renmai.aitojoy.com"
  };

  // 人脉广场-列表页
  static connectionListPage: SEOTestDataItem = {
    title: "企业家 投资人 管理合伙人人脉查询 - 企座",
    keywords: [
      "企业家查询",
      "投资人查询",
      "管理合伙人查询",
      "知名企业家查询"
    ],
    description: "企座人脉广场覆盖超过1亿企业家数据，方便查询您希望寻找的人脉，并支持由平台服务专员为您对接。",
    url: "renmai.aitojoy.com/list"
  };

  /**
   * 人脉广场-详情页
   * @param companyName 企业名称
   * @param personName 人脉姓名
   * @param personId 人脉ID
   * @returns SEO数据对象
   */
  static connectionDetailPage = (
    companyName: string,
    personName: string,
    personId: string
  ) => {
    return {
      title: `${companyName}${personName}个人介绍、任职信息、企业简介、产品/服务、合作需求 - 企座`,
      keywords: [
        `${personName}个人介绍`,
        `${personName}任职信息`,
        `${personName}企业简介`,
        `${personName}产品/服务`,
        `${personName}合作需求`
      ],
      description: `了解${companyName}${personName}个人介绍、任职信息、企业简介、产品/服务、合作需求，可申请平台专员帮您对接人脉！`,
      url: `renmai.aitojoy.com/detail/${personId}`
    };
  };
} 