// @ts-check
import type { Page } from '@playwright/test';
import { expect } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { BasePage } from './base.page';

export class ChuangYeKeTangPage extends BasePage {
  constructor(page: Page, aiFixtures: PlayWrightAiFixtureType) {
    super(page, aiFixtures);
  }

  /**
   * 悬停在企业服务菜单上
   * 该方法使用AI悬停在企业服务按钮上
   */
  async hoverqiyefuwu() {
    // 使用AI悬停在企业服务按钮上
    await this.aiHover('企服广场按钮');
  }

  /**
   * 点击创业课堂链接
   * 该方法使用AI点击企业服务下拉菜单中的"创业课堂"链接
   */
  async dianJiChuangYeKeTang() {
    // 使用AI点击创业课堂链接
    await this.aiTap('企业服务下拉菜单中的"创业课堂"链接');
  }

  /**
   * 选择企业家充电课堂
   * 该方法使用AI点击企业家充电课堂选项
   */
  async xuanZeChongDianKeTang() {
    // 使用AI点击企业家充电课堂
    await this.aiTap('企业家充电课堂选项');
  }

  /**
   * 点击指定名称的专栏
   * @param mingcheng 专栏名称
   */
  async dianJiZhuanLan(mingcheng: string) {
    // 使用AI点击指定名称的专栏
    await this.aiTap(`页面上名称为"${mingcheng}"的专栏卡片或链接`);
  }

  /**
   * 点击企业家充电课堂查看更多按钮
   * 该方法使用AI点击企业家充电课堂右边的查看更多按钮
   */
  async dianJiChaKanGengDuo() {
    await this.aiTap('企业家充电课堂右边的查看更多按钮');
  }

  /**
   * 选择企业培训体系
   * 该方法使用AI点击企业培训体系课程选项
   */
  async xuanZePeiXunXiTong() {
    // 使用AI点击企业培训体系课程
    await this.aiTap('企业培训体系课程选项');
  }

  /**
   * 点击课程分类
   * @param categoryName 分类名称
   */
  async dianJiKeChengFenLei(categoryName: string) {
    // 使用AI点击课程分类
    await this.aiTap(`"${categoryName}"课程分类`);
  }

  /**
   * 搜索课程
   * @param keyword 搜索关键词
   */
  async souSuoKeCheng(keyword: string) {
    // 使用AI在搜索框中输入关键词
    await this.aiInput(keyword, '搜索框');
    
    // 使用AI点击搜索按钮
    await this.aiTap('搜索按钮');
  }

  /**
   * 获取搜索结果数量
   * @returns 搜索结果数量
   */
  async huoQuSouSuoJieGuoShuLiang(): Promise<number> {
    // 使用AI获取搜索结果数量
    const result = await this.ai('搜索结果的数量是多少');
    return parseInt(result.text || '0', 10);
  }

  /**
   * 清空搜索框
   */
  async qingKongSouSuoKuang() {
    // 使用AI清空搜索框
    await this.ai('清空搜索框内容');
  }

  /**
   * 收藏课程
   * @param courseName 课程名称
   */
  async shouCangKeCheng(courseName: string) {
    // 使用AI点击收藏按钮
    await this.aiTap(`收藏"${courseName}"课程`);
  }

  /**
   * 检查课程是否已被收藏
   * @param courseName 课程名称
   * @returns 如果课程已被收藏返回true，否则返回false
   */
  async jianChaKeChengShiFouYiShouCang(courseName: string): Promise<boolean> {
    // 使用AI检查课程是否已收藏
    const result = await this.ai(`"${courseName}"课程是否已被收藏`);
    return result.text?.includes('已收藏') || false;
  }

  /**
   * 取消收藏课程
   * @param courseName 课程名称
   */
  async quXiaoShouCangKeCheng(courseName: string) {
    // 使用AI点击取消收藏按钮
    await this.aiTap(`取消收藏"${courseName}"课程`);
  }

  /**
   * 播放课程
   * @param courseName 课程名称
   */
  async boFangKeCheng(courseName: string) {
    // 使用AI点击播放按钮
    await this.aiTap(`播放"${courseName}"课程`);
  }

  /**
   * 暂停播放
   */
  async zanTingBoFang() {
    // 使用AI点击暂停按钮
    await this.aiTap('暂停播放');
  }

  /**
   * 检查视频是否正在播放
   * @returns 如果视频正在播放返回true，否则返回false
   */
  async jianChaShiFouZhengZaiBoFang(): Promise<boolean> {
    // 使用AI检查视频是否正在播放
    const result = await this.ai('视频是否正在播放');
    return result.text?.includes('正在播放') || false;
  }

  /**
   * 跳转到指定时间点
   * @param seconds 时间点（秒）
   */
  async tiaoZhuanDaoShiJianDian(seconds: number) {
    // 使用AI跳转到指定时间点
    await this.ai(`跳转到${seconds}秒`);
  }

  /**
   * 打开课程评论区域
   * @param courseName 课程名称
   */
  async daKaiKeChengPingLunQuYu(courseName: string) {
    // 使用AI打开评论区域
    await this.aiTap(`打开"${courseName}"课程的评论区域`);
  }

  /**
   * 输入评论内容
   * @param content 评论内容
   */
  async shuRuPingLunNeiRong(content: string) {
    // 使用AI在评论框中输入内容
    await this.aiInput(content, '评论框');
  }

  /**
   * 提交评论
   */
  async tiJiaoPingLun() {
    // 使用AI点击提交评论按钮
    await this.aiTap('提交评论');
  }

  /**
   * 检查评论是否成功发布
   * @param content 评论内容
   * @returns 如果评论发布成功返回true，否则返回false
   */
  async jianChaPingLunShiFouChengGongFaBu(content: string): Promise<boolean> {
    // 使用AI检查评论是否发布成功
    const result = await this.ai(`检查评论"${content}"是否已成功发布`);
    return result.text?.includes('已发布') || result.text?.includes('成功') || false;
  }

  /**
   * 删除评论
   * @param content 评论内容
   */
  async shanChuPingLun(content: string) {
    // 使用AI点击删除评论按钮
    await this.aiTap(`删除评论"${content}"`);
    
    // 使用AI点击确认删除按钮
    await this.aiTap('确认删除');
  }

  /**
   * 检查推荐课程是否显示正确
   * @returns 如果推荐课程显示正确返回true，否则返回false
   */
  async jianChaTuiJianKeChengShiFouZhengQue(): Promise<boolean> {
    // 使用AI检查推荐课程是否显示正确
    const result = await this.ai('检查推荐课程是否显示正确');
    return result.text?.includes('正确') || result.text?.includes('通过') || false;
  }

  /**
   * 点击推荐课程
   * @param courseIndex 课程索引（从0开始）
   */
  async dianJiTuiJianKeCheng(courseIndex: number) {
    // 使用AI点击第N个推荐课程
    await this.aiTap(`第${courseIndex + 1}个推荐课程`);
  }

  /**
   * 刷新推荐列表
   */
  async shuaXinTuiJianLieBiao() {
    // 使用AI点击刷新推荐列表按钮
    await this.aiTap('刷新推荐列表');
  }
}