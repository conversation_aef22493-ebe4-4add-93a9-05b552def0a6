import type { Page } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { BasePage } from './base.page';

export class KeChengListPage extends BasePage {
  constructor(page: Page, aiFixtures: PlayWrightAiFixtureType) {
    super(page, aiFixtures);
  }

  /**
   * 在搜索框中输入搜索关键词
   * @param keyword 要搜索的关键词
   */
  async shuRuSouSuoGuanJianCi(keyword: string) {
    // 使用AI在搜索框中输入关键词
    await this.ai(`在页面顶部或课程区域找到的搜索输入框中输入"${keyword}"`);
  }

  /**
   * 点击搜索按钮
   * 该方法使用AI点击搜索按钮
   */
  async dianJiSouSuoAnNiu() {
    // 使用AI点击搜索按钮
    await this.aiTap('搜索按钮');
  }

  /**
   * 执行搜索操作（输入关键词并点击搜索）
   * @param keyword 要搜索的关键词
   */
  async souSuoKeCheng(keyword: string) {
    await this.shuRuSouSuoGuanJianCi(keyword);
    await this.dianJiSouSuoAnNiu();
  }

  /**
   * 获取搜索结果数量
   * @returns 搜索结果数量
   */
  async huoQuSouSuoJieGuoShuLiang(): Promise<number> {
    // 使用AI获取搜索结果数量
    const result = await this.aiQuery('搜索结果的数量是多少');
    return parseInt(result.text || '0', 10);
  }

  /**
   * 清空搜索框
   * 该方法使用AI清空搜索框中的内容
   */
  async qingKongSouSuoKuang() {
    // 使用AI清空搜索框
    await this.ai('清空搜索框中的内容');
  }

  /**
   * 在课程页，根据分类名称点击一个分类
   * @param categoryName 要点击的分类名称
   */
  async dianJiKeChengFenLei(categoryName: string) {
    // 该方法现在只负责点击操作
    await this.aiTap(`页面上分类为"${categoryName}"的导航链接或标签`);
  }

  /**
   * 收藏指定名称的课程
   * @param courseName 课程名称
   */
  async shouCangKeCheng(courseName: string) {
    // 使用AI点击收藏按钮
    await this.aiTap(`页面上名称为"${courseName}"的课程卡片或标题旁边的收藏按钮`);
  }

  /**
   * 取消收藏指定名称的课程
   * @param courseName 课程名称
   */
  async quXiaoShouCangKeCheng(courseName: string) {
    // 使用AI点击已收藏按钮以取消收藏
    await this.aiTap(`名称为"${courseName}"的课程的已收藏按钮`);
  }

  /**
   * 检查课程是否已被收藏
   * @param courseName 课程名称
   * @returns 如果课程已被收藏返回true，否则返回false
   */
  async jianChaKeChengShiFouYiShouCang(courseName: string): Promise<boolean> {
    // 使用AI检查课程是否已收藏
    const result = await this.ai(`名称为"${courseName}"的课程是否已被收藏`);
    // 根据AI返回的结果判断，这里假设AI会返回包含"已收藏"或"未收藏"的文本
    return result.text?.includes('已收藏') || false;
  }

  /**
   * 导航到我的收藏页面
   * 该方法使用AI点击页面中的"我的收藏"链接
   */
  async daoHangDaoWoDeShouCang() {
    // 使用AI点击我的收藏链接
    await this.aiTap('页面中的"我的收藏"链接');
  }

  /**
   * 播放指定课程
   * @param courseName 课程名称
   */
  async boFangKeCheng(courseName: string) {
    // 使用AI点击播放按钮
    await this.aiTap(`名称为"${courseName}"的课程的播放按钮`);
  }

  /**
   * 暂停播放
   * 该方法使用AI点击视频播放器的暂停按钮
   */
  async zanTingBoFang() {
    // 使用AI点击暂停按钮
    await this.aiTap('视频播放器的暂停按钮');
  }

  /**
   * 检查视频是否正在播放
   * @returns 如果视频正在播放返回true，否则返回false
   */
  async jianChaShiFouZhengZaiBoFang(): Promise<boolean> {
    // 使用AI检查视频是否正在播放
    const result = await this.ai('视频是否正在播放');
    // 根据AI返回的结果判断，这里假设AI会返回包含"正在播放"或"已暂停"的文本
    return result.text?.includes('正在播放') || false;
  }

  /**
   * 跳转到指定时间点
   * @param seconds 要跳转到的时间点（秒）
   */
  async tiaoZhuanDaoShiJianDian(seconds: number) {
    // 使用AI跳转到指定时间点
    await this.ai(`将视频跳转到${seconds}秒`);
  }

  /**
   * 打开课程评论区域
   * @param courseName 课程名称
   */
  async daKaiKeChengPingLunQuYu(courseName: string) {
    // 使用AI点击评论区域按钮
    await this.aiTap(`页面上名称为"${courseName}"的课程的评论区域或评论按钮`);
  }

  /**
   * 输入评论内容
   * @param content 评论内容
   */
  async shuRuPingLunNeiRong(content: string) {
    // 使用AI在评论框中输入内容
    await this.ai(`在评论输入框中输入"${content}"`);
  }

  /**
   * 提交评论
   * 该方法使用AI点击提交评论按钮
   */
  async tiJiaoPingLun() {
    // 使用AI点击提交评论按钮
    await this.aiTap('提交评论按钮');
  }

  /**
   * 检查评论是否成功发布
   * @param content 评论内容
   * @returns 如果评论发布成功返回true，否则返回false
   */
  async jianChaPingLunShiFouChengGongFaBu(content: string): Promise<boolean> {
    // 使用AI检查评论是否发布成功
    const result = await this.ai(`检查包含"${content}"的评论是否已成功发布`);
    // 根据AI返回的结果判断
    return result.text?.includes('已发布') || result.text?.includes('成功') || false;
  }

  /**
   * 删除评论
   * @param content 评论内容
   */
  async shanChuPingLun(content: string) {
    // 使用AI点击删除评论按钮
    await this.aiTap(`包含"${content}"的评论的删除按钮`);
    // 使用AI点击确认删除按钮
    await this.aiTap('确认删除评论的按钮');
  }

  /**
   * 查看推荐课程列表
   * @returns 推荐课程元素数组
   */
  async chaKanTuiJianKeChengLieBiao() {
    // 使用AI获取推荐课程列表
    const result = await this.ai('获取页面上的推荐课程列表');
    return result.elements || [];
  }

  /**
   * 点击推荐课程
   * @param courseIndex 课程索引（从0开始）
   */
  async dianJiTuiJianKeCheng(courseIndex: number) {
    // 使用AI点击第N个推荐课程
    await this.aiTap(`第${courseIndex + 1}个推荐课程`);
  }

  /**
   * 检查推荐课程是否显示正确
   * @returns 如果推荐课程显示正确返回true，否则返回false
   */
  async jianChaTuiJianKeChengShiFouZhengQue(): Promise<boolean> {
    // 使用AI检查推荐课程是否显示正确
    const result = await this.ai('检查推荐课程是否显示正确，至少显示3个推荐课程');
    // 根据AI返回的结果判断
    return result.text?.includes('正确') || result.text?.includes('通过') || false;
  }

  /**
   * 刷新推荐列表
   * 该方法使用AI点击刷新推荐列表按钮
   */
  async shuaXinTuiJianLieBiao() {
    // 使用AI点击刷新推荐列表按钮
    await this.aiTap('刷新推荐列表按钮');
  }
}