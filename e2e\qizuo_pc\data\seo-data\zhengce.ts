/**
 * 惠企政策SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class ZhengceSEOData extends BaseSEOData {
  // 惠企政策-频道页
  static policyChannelPage: SEOTestDataItem = {
    title: `${new Date().getFullYear()}热点政策大全 AI政策解读 政府奖励政策代理申报 - 企座`,
    keywords: [
      `${new Date().getFullYear()}热点政策大全`,
      "AI政策解读",
      "奖励政策代理申报"
    ],
    description: "实时更新全国各省市中小企业补贴、高新企业奖励、产业扶持资金等惠企政策，提供政策匹配、智能解读、代理申报全流程服务，累积帮助企业申领补贴超10亿元。",
    url: "zhengce.aitojoy.com"
  };

  // 惠企政策-列表页-奖补政策tab
  static policyRewardListPage: SEOTestDataItem = {
    title: "各省奖励补贴政策查询 AI解读 代理申报 - 企座",
    keywords: [
      "奖励补贴政策查询",
      "奖励补贴政策AI解读",
      "奖励补贴政策代理申报"
    ],
    description: "实时汇总全国最新奖励补贴政策，通过AI大模型进行政策背景、政策价值等多维度解读，轻松为您的企业找到合适的奖励补贴政策。",
    url: "zhengce.aitojoy.com/jiangbu"
  };

  /**
   * 惠企政策-列表页-奖补政策tab-发布地区筛选
   * @param region 发布地区名称
   * @param regionPinyin 发布地区全拼
   * @returns SEO数据对象
   */
  static policyRewardListPageRegionFilter = (region: string, regionPinyin: string) => {
    return {
      title: `${region}奖励补贴政策查询 AI解读 代理申报 - 企座`,
      keywords: [
        `${region}奖励补贴政策查询`,
        `${region}奖励补贴政策AI解读`,
        `${region}奖励补贴政策代理申报`
      ],
      description: `实时汇总${region}最新奖励补贴政策，通过AI大模型进行政策背景、政策价值等多维度解读，轻松为您的企业找到合适的奖励补贴政策。`,
      url: `zhengce.aitojoy.com/jiangbu/diqu-${regionPinyin}`
    };
  };

  /**
   * 惠企政策-列表页-奖补政策tab-政策类型筛选
   * @param policyType 政策类型
   * @param policyTypePinyin 政策类型全拼
   * @returns SEO数据对象
   */
  static policyRewardListPageTypeFilter = (policyType: string, policyTypePinyin: string) => {
    return {
      title: `各省${policyType}奖励补贴政策查询 AI解读 代理申报 - 企座`,
      keywords: [
        `${policyType}奖励补贴政策查询`,
        `${policyType}奖励补贴政策AI解读`,
        `${policyType}奖励补贴政策代理申报`
      ],
      description: `实时汇总${policyType}最新奖励补贴政策，通过AI大模型进行政策背景、政策价值等多维度解读，轻松为您的企业找到合适的奖励补贴政策。`,
      url: `zhengce.aitojoy.com/jiangbu/leixing-${policyTypePinyin}`
    };
  };

  /**
   * 惠企政策-列表页-奖补政策tab-组合筛选
   * @param region 发布地区名称
   * @param policyType 政策类型
   * @param regionPinyin 发布地区全拼
   * @param policyTypePinyin 政策类型全拼
   * @returns SEO数据对象
   */
  static policyRewardListPageCombinedFilter = (
    region: string = '',
    policyType: string = '',
    regionPinyin: string = '',
    policyTypePinyin: string = ''
  ) => {
    return {
      title: `${region}${policyType}奖励补贴政策查询 AI解读 代理申报 - 企座`,
      keywords: [
        `${region}${policyType}奖励补贴政策查询`,
        `${region}${policyType}奖励补贴政策AI解读`,
        `${region}${policyType}奖励补贴政策代理申报`
      ],
      description: `实时汇总${region}${policyType}最新奖励补贴政策，通过AI大模型进行政策背景、政策价值等多维度解读，轻松为您的企业找到合适的奖励补贴政策。`,
      url: `zhengce.aitojoy.com/jiangbu/leixing-${policyTypePinyin}-diqu-${regionPinyin}`
    };
  };

  // 惠企政策-列表页-热点政策tab
  static policyHotListPage: SEOTestDataItem = {
    title: "各省热点政策查询 AI解读 - 企座",
    keywords: [
      "热点政策查询",
      "热点政策AI解读"
    ],
    description: "提供全国最新热点政策，通过AI大模型进行政策背景、政策价值解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策",
    url: "zhengce.aitojoy.com/redian"
  };

  /**
   * 惠企政策-列表页-热点政策tab-发布地区筛选
   * @param region 发布地区名称
   * @param regionPinyin 发布地区全拼
   * @returns SEO数据对象
   */
  static policyHotListPageRegionFilter = (region: string, regionPinyin: string) => {
    return {
      title: `${region}热点政策查询 AI解读 - 企座`,
      keywords: [
        `${region}热点政策查询`,
        `${region}热点政策AI解读`
      ],
      description: `提供${region}最新热点政策，通过AI大模型进行政策背景、政策价值解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策`,
      url: `zhengce.aitojoy.com/redian/diqu-${regionPinyin}`
    };
  };

  /**
   * 惠企政策-列表页-热点政策tab-适用行业筛选
   * @param industry 适用行业
   * @param industryPinyin 适用行业全拼
   * @returns SEO数据对象
   */
  static policyHotListPageIndustryFilter = (industry: string, industryPinyin: string) => {
    return {
      title: `各省${industry}热点政策查询 AI解读 - 企座`,
      keywords: [
        `${industry}热点政策查询`,
        `${industry}热点政策AI解读`
      ],
      description: `提供${industry}最新热点政策，通过AI大模型进行政策背景、政策价值解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策`,
      url: `zhengce.aitojoy.com/redian/hangye-${industryPinyin}`
    };
  };

  /**
   * 惠企政策-列表页-热点政策tab-政策类型筛选
   * @param policyType 政策类型
   * @param policyTypePinyin 政策类型全拼
   * @returns SEO数据对象
   */
  static policyHotListPageTypeFilter = (policyType: string, policyTypePinyin: string) => {
    return {
      title: `各省${policyType}热点政策查询 AI解读 - 企座`,
      keywords: [
        `${policyType}热点政策查询`,
        `${policyType}热点政策AI解读`
      ],
      description: `提供${policyType}最新热点政策，通过AI大模型进行政策背景、政策价值解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策`,
      url: `zhengce.aitojoy.com/redian/leixing-${policyTypePinyin}`
    };
  };

  /**
   * 惠企政策-列表页-热点政策tab-组合筛选
   * @param region 发布地区名称
   * @param industry 适用行业
   * @param policyType 政策类型
   * @param regionPinyin 发布地区全拼
   * @param industryPinyin 适用行业全拼
   * @param policyTypePinyin 政策类型全拼
   * @returns SEO数据对象
   */
  static policyHotListPageCombinedFilter = (
    region: string = '',
    industry: string = '',
    policyType: string = '',
    regionPinyin: string = '',
    industryPinyin: string = '',
    policyTypePinyin: string = ''
  ) => {
    return {
      title: `${region}${industry}${policyType}热点政策查询 AI解读 代理申报 - 企座`,
      keywords: [
        `${region}${industry}${policyType}热点政策查询`,
        `${region}${industry}${policyType}热点政策AI解读`,
        `${region}${industry}${policyType}热点政策代理申报`
      ],
      description: `实时汇总${region}${industry}${policyType}最新热点政策，通过AI大模型进行政策背景、政策价值等多维度解读，涉及人工智能、电子信息、新材料等30余个不同行业，包含人才补贴、企业研发投入补贴等50余项不同政策类型，帮助您快速找到有价值的政策。`,
      url: `zhengce.aitojoy.com/redian/leixing-${policyTypePinyin}-diqu-${regionPinyin}-hangye-${industryPinyin}`
    };
  };

  /**
   * 惠企政策-详情页-奖补政策
   * @param policyName 政策名称
   * @param policyId 政策ID
   * @returns SEO数据对象
   */
  static policyRewardDetailPage = (policyName: string, policyId: string) => {
    return {
      title: `奖补政策${policyName}AI解读、要点介绍、申报流程 - 企座`,
      keywords: [
        `${policyName}AI解读`,
        `${policyName}要点介绍`,
        `${policyName}申报流程`
      ],
      description: `为您详细解读${policyName}背景、价值及其它相关要点，支持政策原文查阅。可在平台直接申请代理申报服务。`,
      url: `zhengce.aitojoy.com/jiangbu/${policyId}`
    };
  };

  /**
   * 惠企政策-详情页-热点政策
   * @param policyName 政策名称
   * @param policyId 政策ID
   * @returns SEO数据对象
   */
  static policyHotDetailPage = (policyName: string, policyId: string) => {
    return {
      title: `热点政策${policyName}AI解读、政策原文 - 企座`,
      keywords: [
        `${policyName}AI解读`,
        `${policyName}原文内容`
      ],
      description: `为您详细解读${policyName}背景、价值及其它相关要点，支持政策原文查阅。`,
      url: `zhengce.aitojoy.com/redian/${policyId}`
    };
  };
} 