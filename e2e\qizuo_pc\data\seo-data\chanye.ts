/**
 * 产业服务SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class ChanyeSEOData extends BaseSEOData {
  // 产业服务-频道页
  static industryServiceChannelPage: SEOTestDataItem = {
    title: "匹配并购标的 探索产业全貌 研究地域产业集群 - 企座",
    keywords: [
      "上市公司并购推荐",
      "中小企业并购匹配",
      "产业链大数据",
      "并购标的筛选",
      "并购撮合服务"
    ],
    description: "企座平台依托产业链与企业大数据及自研大模型算法，为上市公司精准推荐并购标的，助力中小创新企业智能匹配并购方。覆盖人工智能、新能源等150+行业，提供标的筛选、估值分析、交易撮合全流程服务。",
    url: "chanyefuwu.aitojoy.com"
  };

  // 产业服务-列表页
  static industryServiceListPage: SEOTestDataItem = {
    title: "并购标的查询 有并购需求上市公司查询 企业产业信息查询 - 企座",
    keywords: [
      "并购需求上市公司",
      "中小企业被并购",
      "企业涉及产业信息"
    ],
    description: "聚合并购双方企业信息，基于企业与产业链大数据，提供对应企业全方位信息。有并购需求的双方企业提交资料即可获得凭条提供的目标企业筛选、匹配、对接服务。",
    url: "chanyefuwu.aitojoy.com/list"
  };

  /**
   * 产业服务-列表页-所在地筛选
   * @param region 地区名称
   * @param otherFilters 其它筛选条件全拼（可选）
   * @returns SEO数据对象
   */
  static industryServiceListPageRegionFilter = (region: string, otherFilters: string = '') => {
    // 将中文地区名称转换为拼音
    const regionPinyin = BaseSEOData.chineseToPinyin(region);
    const urlPart = otherFilters ? `${regionPinyin}-${otherFilters}` : regionPinyin;

    return {
      title: `${region}并购标的查询 有并购需求上市公司查询 企业产业信息查询 - 企座`,
      keywords: [
        `${region}并购需求上市公司`,
        `${region}中小企业被并购`,
        `${region}企业涉及产业信息`
      ],
      description: `聚合${region}并购双方企业信息，基于企业与产业链大数据，提供对应企业全方位信息。有并购需求的双方企业提交资料即可获得凭条提供的目标企业筛选、匹配、对接服务。`,
      url: `chanyefuwu.aitojoy.com/list/diqu-${urlPart}`
    };
  };

  /**
   * 产业服务-详情页
   * @param companyName 企业名称
   * @param companyId 企业ID
   * @returns SEO数据对象
   */
  static industryServiceDetailPage = (companyName: string, companyId: string) => {
    return {
      title: `${companyName}基本信息 涉足产业链 经营信息 企业舆情 知识产权 法律风险 - 企座`,
      keywords: [
        "企业基本信息",
        "企业涉足产业链",
        "企业经营信息",
        "企业舆情",
        "企业知识产权",
        "企业法律风险"
      ],
      description: `${companyName}基本信息、涉足产业链、经营信息、企业舆情、知识产权、法律风险，支持有并购合作需求企业进行对接。`,
      url: `chanyefuwu.aitojoy.com/detail/${companyId}`
    };
  };
} 