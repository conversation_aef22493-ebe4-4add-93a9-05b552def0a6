import { test as base } from "@playwright/test";
import type { PlayWrightAiFixtureType } from "@midscene/web/playwright";
import { PlaywrightAiFixture } from "@midscene/web/playwright";
import path from "path";
import { envConfig } from "../qizuo_pc/config/environment.config";

/**
 * 根据环境获取认证文件路径
 * @param type 认证类型 ('pc' | 'admin')
 * @returns 认证文件路径
 */
function getAuthFilePath(type: string): string {
  const env = envConfig.getEnvironment();
  return path.join(process.cwd(), `data/qizuo-${type}-auth-${env}.json`);
}

// 扩展测试固件，使用默认浏览器配置
export const test = base.extend<PlayWrightAiFixtureType & {
}>({
  ...PlaywrightAiFixture(),
  // 使用默认浏览器配置
  context: async ({ browser }, use) => {
    const context = await browser.newContext({
      storageState: getAuthFilePath('admin')
    });
    await use(context);
  },
});