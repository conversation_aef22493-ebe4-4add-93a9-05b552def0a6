// @ts-check
import type { Page } from '@playwright/test';
import { expect } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';
import { BasePage } from './base.page';

export class RenMaiGuangChangPage extends BasePage {
  constructor(page: Page, aiFixtures: PlayWrightAiFixtureType) {
    super(page, aiFixtures);
  }

  /**
   * 导航到人脉广场页面
   */
  async navigateToRenMaiGuangChang() {
    await this.page.goto('https://test-www.aitojoy.com/connections/home');
    // 使用AI等待页面加载完成
    await this.aiWaitFor('页面已完全加载', { timeoutMs: 5000 });
  }

  /**
   * 验证页面标题包含预期文本
   */
  async verifyPageTitle(expectedTitle: string) {
    // 使用AI断言验证标题
    await this.aiAssert(`页面标题包含"${expectedTitle}"`);
  }

  /**
   * 点击导航栏中的按钮
   */
  async clickNavButton(buttonName: string) {
    // 使用AI点击导航按钮
    await this.aiTap(`导航栏中的"${buttonName}"按钮`);
  }

  /**
   * 搜索人脉
   */
  async searchConnection(keyword: string) {
    // 使用AI输入搜索关键词
    await this.aiInput(keyword, '搜索框');
    // 使用AI按下回车键
    await this.ai('按下回车键');
  }

  /**
   * 验证搜索结果包含特定文本
   */
  async verifySearchResults(expectedText: string) {
    // 使用AI断言验证搜索结果
    await this.aiAssert(`搜索结果中包含"${expectedText}"`);
  }

  /**
   * 点击筛选选项
   */
  async clickFilterOption(optionName: string) {
    // 使用AI点击筛选选项
    await this.aiTap(`筛选选项"${optionName}"`);
  }

  /**
   * 验证人脉列表是否可见
   */
  async verifyConnectionListVisible() {
    // 使用AI断言验证人脉列表是否可见
    await this.aiAssert('人脉列表已显示在页面上');
  }
}