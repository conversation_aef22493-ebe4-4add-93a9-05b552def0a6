{"cookies": [{"name": "acw_tc", "value": "0a27a9a017489406998675939e005e416c29274eff7289e9c2a7c02b2880fd", "domain": "admin.aitojoy.com", "path": "/", "expires": 1748942499.651223, "httpOnly": true, "secure": false, "sameSite": "Lax"}], "origins": [{"origin": "https://admin.aitojoy.com", "localStorage": [{"name": "_AMap_AMap.Geolocation", "value": "{\"version\":\"1736748443937\",\"script\":\"!function(s){\\\"use strict\\\";var i=function(t,e){if(!(t instanceof e))throw new TypeError(\\\"Cannot call a class as a function\\\")};function o(t,e){for(var n=0;n<e.length;n++){var o=e[n];o.enumerable=o.enumerable||!1,o.configurable=!0,\\\"value\\\"in o&&(o.writable=!0),Object.defineProperty(t,o.key,o)}}var a=function(t,e,n){return e&&o(t.prototype,e),n&&o(t,n),t};function t(t,e){return t(e={exports:{}},e.exports),e.exports}var n=t(function(e){function n(t){return\\\"function\\\"==typeof Symbol&&\\\"symbol\\\"==typeof Symbol.iterator?e.exports=n=function(t){return typeof t}:e.exports=n=function(t){return t&&\\\"function\\\"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?\\\"symbol\\\":typeof t},n(t)}e.exports=n});var r=function(t){if(void 0===t)throw new ReferenceError(\\\"this hasn't been initialised - super() hasn't been called\\\");return t};var c=function(t,e){return!e||\\\"object\\\"!==n(e)&&\\\"function\\\"!=typeof e?r(t):e},u=t(function(e){function n(t){return e.exports=n=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},n(t)}e.exports=n}),l=t(function(n){function o(t,e){return n.exports=o=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},o(t,e)}n.exports=o});var f=function(t,e){if(\\\"function\\\"!=typeof e&&null!==e)throw new TypeError(\\\"Super expression must either be null or a function\\\");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&l(t,e)};var p=function(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t},g=t(function(t){var e=function(a){var c,t=Object.prototype,l=t.hasOwnProperty,e=\\\"function\\\"==typeof Symbol?Symbol:{},r=e.iterator||\\\"@@iterator\\\",n=e.asyncIterator||\\\"@@asyncIterator\\\",o=e.toStringTag||\\\"@@toStringTag\\\";function s(t,e,n,o){var i,a,s,c,r=e&&e.prototype instanceof y?e:y,u=Object.create(r.prototype),l=new P(o||[]);return u._invoke=(i=t,a=n,s=l,c=p,function(t,e){if(c===d)throw new Error(\\\"Generator is already running\\\");if(c===g){if(\\\"throw\\\"===t)throw e;return A()}for(s.method=t,s.arg=e;;){var n=s.delegate;if(n){var o=L(n,s);if(o){if(o===m)continue;return o}}if(\\\"next\\\"===s.method)s.sent=s._sent=s.arg;else if(\\\"throw\\\"===s.method){if(c===p)throw c=g,s.arg;s.dispatchException(s.arg)}else\\\"return\\\"===s.method&&s.abrupt(\\\"return\\\",s.arg);c=d;var r=f(i,a,s);if(\\\"normal\\\"===r.type){if(c=s.done?g:h,r.arg===m)continue;return{value:r.arg,done:s.done}}\\\"throw\\\"===r.type&&(c=g,s.method=\\\"throw\\\",s.arg=r.arg)}}),u}function f(t,e,n){try{return{type:\\\"normal\\\",arg:t.call(e,n)}}catch(t){return{type:\\\"throw\\\",arg:t}}}a.wrap=s;var p=\\\"suspendedStart\\\",h=\\\"suspendedYield\\\",d=\\\"executing\\\",g=\\\"completed\\\",m={};function y(){}function i(){}function u(){}var v={};v[r]=function(){return this};var w=Object.getPrototypeOf,b=w&&w(w(S([])));b&&b!==t&&l.call(b,r)&&(v=b);var _=u.prototype=y.prototype=Object.create(v);function k(t){[\\\"next\\\",\\\"throw\\\",\\\"return\\\"].forEach(function(e){t[e]=function(t){return this._invoke(e,t)}})}function x(c,u){var e;this._invoke=function(n,o){function t(){return new u(function(t,e){!function e(t,n,o,r){var i=f(c[t],c,n);if(\\\"throw\\\"!==i.type){var a=i.arg,s=a.value;return s&&\\\"object\\\"==typeof s&&l.call(s,\\\"__await\\\")?u.resolve(s.__await).then(function(t){e(\\\"next\\\",t,o,r)},function(t){e(\\\"throw\\\",t,o,r)}):u.resolve(s).then(function(t){a.value=t,o(a)},function(t){return e(\\\"throw\\\",t,o,r)})}r(i.arg)}(n,o,t,e)})}return e=e?e.then(t,t):t()}}function L(t,e){var n=t.iterator[e.method];if(n===c){if(e.delegate=null,\\\"throw\\\"===e.method){if(t.iterator.return&&(e.method=\\\"return\\\",e.arg=c,L(t,e),\\\"throw\\\"===e.method))return m;e.method=\\\"throw\\\",e.arg=new TypeError(\\\"The iterator does not provide a 'throw' method\\\")}return m}var o=f(n,t.iterator,e.arg);if(\\\"throw\\\"===o.type)return e.method=\\\"throw\\\",e.arg=o.arg,e.delegate=null,m;var r=o.arg;return r?r.done?(e[t.resultName]=r.value,e.next=t.nextLoc,\\\"return\\\"!==e.method&&(e.method=\\\"next\\\",e.arg=c),e.delegate=null,m):r:(e.method=\\\"throw\\\",e.arg=new TypeError(\\\"iterator result is not an object\\\"),e.delegate=null,m)}function O(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function C(t){var e=t.completion||{};e.type=\\\"normal\\\",delete e.arg,t.completion=e}function P(t){this.tryEntries=[{tryLoc:\\\"root\\\"}],t.forEach(O,this),this.reset(!0)}function S(e){if(e){var t=e[r];if(t)return t.call(e);if(\\\"function\\\"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function t(){for(;++n<e.length;)if(l.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=c,t.done=!0,t};return o.next=o}}return{next:A}}function A(){return{value:c,done:!0}}return i.prototype=_.constructor=u,u.constructor=i,u[o]=i.displayName=\\\"GeneratorFunction\\\",a.isGeneratorFunction=function(t){var e=\\\"function\\\"==typeof t&&t.constructor;return!!e&&(e===i||\\\"GeneratorFunction\\\"===(e.displayName||e.name))},a.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,u):(t.__proto__=u,o in t||(t[o]=\\\"GeneratorFunction\\\")),t.prototype=Object.create(_),t},a.awrap=function(t){return{__await:t}},k(x.prototype),x.prototype[n]=function(){return this},a.AsyncIterator=x,a.async=function(t,e,n,o,r){void 0===r&&(r=Promise);var i=new x(s(t,e,n,o),r);return a.isGeneratorFunction(e)?i:i.next().then(function(t){return t.done?t.value:i.next()})},k(_),_[o]=\\\"Generator\\\",_[r]=function(){return this},_.toString=function(){return\\\"[object Generator]\\\"},a.keys=function(n){var o=[];for(var t in n)o.push(t);return o.reverse(),function t(){for(;o.length;){var e=o.pop();if(e in n)return t.value=e,t.done=!1,t}return t.done=!0,t}},a.values=S,P.prototype={constructor:P,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=c,this.done=!1,this.delegate=null,this.method=\\\"next\\\",this.arg=c,this.tryEntries.forEach(C),!t)for(var e in this)\\\"t\\\"===e.charAt(0)&&l.call(this,e)&&!isNaN(+e.slice(1))&&(this[e]=c)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if(\\\"throw\\\"===t.type)throw t.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var o=this;function t(t,e){return i.type=\\\"throw\\\",i.arg=n,o.next=t,e&&(o.method=\\\"next\\\",o.arg=c),!!e}for(var e=this.tryEntries.length-1;0<=e;--e){var r=this.tryEntries[e],i=r.completion;if(\\\"root\\\"===r.tryLoc)return t(\\\"end\\\");if(r.tryLoc<=this.prev){var a=l.call(r,\\\"catchLoc\\\"),s=l.call(r,\\\"finallyLoc\\\");if(a&&s){if(this.prev<r.catchLoc)return t(r.catchLoc,!0);if(this.prev<r.finallyLoc)return t(r.finallyLoc)}else if(a){if(this.prev<r.catchLoc)return t(r.catchLoc,!0)}else{if(!s)throw new Error(\\\"try statement without catch or finally\\\");if(this.prev<r.finallyLoc)return t(r.finallyLoc)}}}},abrupt:function(t,e){for(var n=this.tryEntries.length-1;0<=n;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&l.call(o,\\\"finallyLoc\\\")&&this.prev<o.finallyLoc){var r=o;break}}r&&(\\\"break\\\"===t||\\\"continue\\\"===t)&&r.tryLoc<=e&&e<=r.finallyLoc&&(r=null);var i=r?r.completion:{};return i.type=t,i.arg=e,r?(this.method=\\\"next\\\",this.next=r.finallyLoc,m):this.complete(i)},complete:function(t,e){if(\\\"throw\\\"===t.type)throw t.arg;return\\\"break\\\"===t.type||\\\"continue\\\"===t.type?this.next=t.arg:\\\"return\\\"===t.type?(this.rval=this.arg=t.arg,this.method=\\\"return\\\",this.next=\\\"end\\\"):\\\"normal\\\"===t.type&&e&&(this.next=e),m},finish:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n.finallyLoc===t)return this.complete(n.completion,n.afterLoc),C(n),m}},catch:function(t){for(var e=this.tryEntries.length-1;0<=e;--e){var n=this.tryEntries[e];if(n.tryLoc===t){var o=n.completion;if(\\\"throw\\\"===o.type){var r=o.arg;C(n)}return r}}throw new Error(\\\"illegal catch attempt\\\")},delegateYield:function(t,e,n){return this.delegate={iterator:S(t),resultName:e,nextLoc:n},\\\"next\\\"===this.method&&(this.arg=c),m}},a}(t.exports);try{regeneratorRuntime=e}catch(t){Function(\\\"r\\\",\\\"regeneratorRuntime = r\\\")(e)}});function h(t,e,n,o,r,i,a){try{var s=t[i](a),c=s.value}catch(t){return void n(t)}s.done?e(c):Promise.resolve(c).then(o,r)}var d=function(s){return function(){var t=this,a=arguments;return new Promise(function(e,n){var o=s.apply(t,a);function r(t){h(o,e,n,r,i,\\\"next\\\",t)}function i(t){h(o,e,n,r,i,\\\"throw\\\",t)}r(void 0)})}};function m(o){return function(){var t,e=u(o);if(function(){if(\\\"undefined\\\"==typeof Reflect||!Reflect.construct)return;if(Reflect.construct.sham)return;if(\\\"function\\\"==typeof Proxy)return 1;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),1}catch(t){return}}()){var n=u(this).constructor;t=Reflect.construct(e,arguments,n)}else t=e.apply(this,arguments);return c(this,t)}}function y(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,o)}return n}function v(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?y(Object(n),!0).forEach(function(t){p(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):y(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function w(t,e){var n=(t+\\\"\\\").slice(-2),o=(e+\\\"\\\").slice(-2);t=t.slice(0,-2),e=e.slice(0,-2);var r=parseInt((o+n).slice(1)),i=Math.ceil(r/250)%2?1:-1,a=1<r/500?1:-1;return t-=parseInt(\\\"1\\\"+n)/3e3*i,e-=parseInt(\\\"1\\\"+o)/3e3*a,new s.LngLat(parseFloat(t).toFixed(5),parseFloat(e).toFixed(5))}function b(r,i){return new Promise(function(o,t){var e=r.position;if(\\\"ipcity\\\"!==r.location_type){var n=_.protocol+\\\"://restapi.amap.com/v3/geocode/regeo\\\";s.WebService.get(n,{location:e.toString(),extensions:i},function(t,e){if(\\\"complete\\\"===t){var n={};!e.regeocode||\\\"\\\"===I(e.regeocode.formatted_address)?r.message+=\\\"Get address fail, no address result.\\\":(n=E(e.regeocode),r.message+=\\\"Get address success.\\\"),r=v({},r,{},n)}else r.message+=\\\"Get address fail,check your key or network.\\\";o(r)})}else r.message+=\\\"city center no address.\\\",o(r)})}var e=navigator.userAgent.toLowerCase(),_=s.getConfig(),k=-1!==e.indexOf(\\\"android\\\"),x=-1!==e.indexOf(\\\"mobile\\\"),L=-1!==e.indexOf(\\\"alipay\\\")||-1!==e.indexOf(\\\"ucbrowser\\\")&&k,O=function(){var e=d(g.mark(function t(n){return g.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt(\\\"return\\\",new Promise(function(r,t){var e=_.protocol+\\\"://restapi.amap.com/v3/assistant/coordinate/convert\\\";s.WebService.get(e,{coordsys:\\\"gps\\\",locations:n.toString()},function(t,e){if(\\\"complete\\\"==t)if(1===parseInt(e.status)&&e.locations){var n=e.locations.split(\\\",\\\"),o=new s.LngLat(parseFloat(n[0]),parseFloat(n[1]));r({status:0,location:o,message:\\\"Convert Success.\\\"})}else r({status:1,message:\\\"Convert failed.\\\"});else r({status:1,message:\\\"Convert request failed.\\\"})})}));case 1:case\\\"end\\\":return t.stop()}},t)}));return function(t){return e.apply(this,arguments)}}(),C=function(){var e=d(g.mark(function t(e){return g.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt(\\\"return\\\",new Promise(function(n,t){navigator.geolocation?navigator.geolocation.getCurrentPosition(function(t){if(t.coords&&t.coords.longitude&&t.coords.latitude){var e={status:0,code:0,info:\\\"SUCCESS\\\",position:new s.LngLat(t.coords.longitude,t.coords.latitude),location_type:\\\"html5\\\",message:\\\"Get geolocation success.\\\",accuracy:t.coords.accuracy,isConverted:L,altitude:t.coords.altitude,altitudeAccuracy:t.coords.altitudeAccuracy,heading:t.coords.heading,speed:t.coords.speed};n(e)}else n({status:1,code:2,info:\\\"POSITION_UNAVAILABLE\\\",message:\\\"Get geolocation position unavailable.\\\"})},function(t){1!==t.code?3===t.code?n({status:1,code:3,info:\\\"TIMEOUT\\\",message:\\\"Get geolocation timeout.\\\",originMessage:t.message}):2===t.code&&n({status:1,code:2,info:\\\"POSITION_UNAVAILABLE\\\",message:\\\"Get geolocation position unavailable.\\\",originMessage:t.message}):n({status:1,code:1,info:\\\"PERMISSION_DENIED\\\",message:\\\"Geolocation permission denied.\\\",originMessage:t.message})},e):t({info:\\\"NOT_SUPPORTED\\\",message:\\\"Not Support HTML5 Geolocation.\\\"})}));case 1:case\\\"end\\\":return t.stop()}},t)}));return function(t){return e.apply(this,arguments)}}(),P=function(){var t=d(g.mark(function t(){return g.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt(\\\"return\\\",new Promise(function(n,t){var e=_.protocol+\\\"://webapi.amap.com/maps/ipLocation\\\";s.WebService.get(e,{},function(t,e){\\\"complete\\\"==t?e.status&&null!=e.lng&&null!=e.lat?(e.position=w(e.lng,e.lat),delete e.lng,delete e.lat,e.status=0,e.message=\\\"Get ipLocation success.\\\",e.location_type=\\\"ip\\\",e.accuracy=null,e.isConverted=!0,n(e)):n({status:1,message:\\\"Get ipLocation failed.\\\"}):n({status:1,message:\\\"Request ipLocation failed.\\\"})})}));case 1:case\\\"end\\\":return t.stop()}},t)}));return function(){return t.apply(this,arguments)}}(),S=function(){var t=d(g.mark(function t(){return g.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt(\\\"return\\\",new Promise(function(n,t){var e=_.protocol+\\\"://webapi.amap.com/maps/ipCity\\\";s.WebService.get(e,{},function(t,e){\\\"complete\\\"==t?e.center&&1===e.status?(e.position=e.center,delete e.center,e.location_type=\\\"ipcity\\\",e.isConverted=!0,e.status=0,e.message=\\\"Get city by ip success.\\\",n(e)):n({status:1,message:\\\"Get city by ip failed.\\\"}):n({status:1,message:\\\"Request ipCity failed.\\\"})})}));case 1:case\\\"end\\\":return t.stop()}},t)}));return function(){return t.apply(this,arguments)}}(),A=function(){var e=d(g.mark(function t(e){return g.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt(\\\"return\\\",new Promise(function(n,t){s.Http.JSONP(\\\"http://127.0.0.1:43689/geolocation?to=3000&_=\\\"+(new Date).getTime(),function(t,e){\\\"complete\\\"==t&&e.location&&e.location.x&&e.location.y?n({status:0,position:new s.LngLat(e.location.x,e.location.y),accuracy:e.location.precision,message:\\\"Get sdkLocation success.\\\",location_type:\\\"sdk\\\",isConverted:!0}):n({status:1,message:\\\"Get sdkLocation failed.\\\"})})}));case 1:case\\\"end\\\":return t.stop()}},t)}));return function(t){return e.apply(this,arguments)}}(),G=function(){var e=d(g.mark(function t(n){return g.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:return t.abrupt(\\\"return\\\",new Promise(function(e,t){W.instance.getLocation(n,function(t){t.result&&t.result.x&&t.result.y?e({status:0,position:new s.LngLat(t.result.x,t.result.y),accuracy:t.result.precision,message:\\\"Get sdkLocation success.\\\",location_type:\\\"sdk\\\",isConverted:!0}):e({status:1,sdkErrorInfo:t.errorInfo,sdkLocationDetail:t.locationDetail,message:\\\"Get sdkLocation failed.\\\"})})}));case 1:case\\\"end\\\":return t.stop()}},t)}));return function(t){return e.apply(this,arguments)}}(),E=function(t){var e={addressComponent:{citycode:I(t.addressComponent.citycode),adcode:I(t.addressComponent.adcode),businessAreas:N(t.addressComponent.businessAreas),neighborhoodType:I(t.addressComponent.neighborhood.type),neighborhood:I(t.addressComponent.neighborhood.name),building:I(t.addressComponent.building.name),buildingType:I(t.addressComponent.building.type),street:I(t.addressComponent.streetNumber.street),streetNumber:I(t.addressComponent.streetNumber.number),province:I(t.addressComponent.province),city:I(t.addressComponent.city),district:I(t.addressComponent.district),township:I(t.addressComponent.township)},formattedAddress:I(t.formatted_address),roads:[],crosses:[],pois:[]};if(t.roads&&(e.roads=j(t.roads)),t.roadinters){var n=t.roadinters;D(n)||(n=[t.roadinters.roadinter]),e.crosses=B(n)}return t.pois&&(e.pois=T(t.pois)),t.aois&&(e.aois=M(t.aois)),e},I=function(t){return\\\"object\\\"===n(t)||void 0===t?\\\"\\\":t},j=function(t){for(var e=[],n=0;n<t.length;n+=1){var o=t[n];e[n]={id:o.id,name:o.name,distance:F(o.distance),direction:I(o.direction),location:R(o.location)}}return e},N=function(t){var e=[];if(t)for(var n=0;n<t.length;n+=1)t[n].id&&e.push({name:I(t[n].name),id:I(t[n].id),location:R(t[n].location)});return e},B=function(t){for(var e=[],n=0;n<t.length;n+=1){var o=t[n];e[n]={distance:F(o.distance),direction:I(o.direction),first_id:I(o.first_id),first_name:I(o.first_name),second_name:I(o.second_name),second_id:I(o.second_id),location:R(o.location)}}return e},T=function(t){for(var e=[],n=0;n<t.length;n+=1){var o=t[n];e[n]={id:o.id,name:o.name,type:o.type,address:I(o.address),distance:F(o.distance),tel:I(o.tel),location:R(o.location),direction:I(o.direction),businessArea:I(o.businessarea)}}return e},M=function(t){for(var e=[],n=0,o=t.length;n<o;n++){var r=t[n];e.push({adcode:I(r.adcode),area:I(r.area),id:I(r.id),location:R(r.location),name:I(r.name),type:I(r.type)})}return e},F=function(t){var e=parseInt(t,10);return isNaN(e)?0:e},R=function(t){var e=t.split(\\\",\\\");return new s.LngLat(e[0],e[1])},D=function(t){return\\\"[object Array]\\\"===Object.prototype.toString.call(t)},U=function(t){f(r,t);var e,o=m(r);function r(t){var e;i(this,r);var n=v({position:\\\"RB\\\",offset:[15,15],borderColor:\\\"silver\\\",borderRadius:\\\"50%\\\",buttonSize:\\\"32px\\\",convert:!0,needAddress:!1,enableHighAccuracy:!1,timeout:1e4,maximumAge:0,showButton:!0,showCircle:!0,showMarker:!0,markerOptions:{innerOverlay:!0,offset:new s.Pixel(-10,-10),content:\\\"<img class='amap-geolocation-marker' src='https://a.amap.com/jsapi/static/image/plugin/point.png'/>\\\"},circleOptions:{innerOverlay:!0,strokeColor:\\\"#0093FF\\\",noSelect:!0,strokeOpacity:.5,strokeWeight:1,fillColor:\\\"#02B0FF\\\",fillOpacity:.25},panToLocation:!0,zoomToAccuracy:!1,GeoLocationFirst:x,noIpLocate:0,noGeoLocation:0,useNative:!1,getCityWhenFail:!1,extensions:\\\"base\\\"},t);switch((e=o.call(this,n))._className=\\\"geolocation\\\",e.initContainer(),e.CLASS_NAME=\\\"AMap.Geolocation\\\",e._locateCallBacks=[],e._watchCallBacks=[],e._locating=!1,e._config.noGeoLocation){case 0:e.useGeoLocation=!0;break;case 1:e.useGeoLocation=!x;break;case 2:e.useGeoLocation=x;break;case 3:e.useGeoLocation=!1;break;default:e.useGeoLocation=!0}switch(e._config.noIpLocate){case 0:e.useIPLocation=!0;break;case 1:e.useIPLocation=!x;break;case 2:e.useIPLocation=x;break;case 3:case!0:e.useIPLocation=!1;break;default:e.useIPLocation=!0}return e.initUI(),e}return a(r,[{key:\\\"initUI\\\",value:function(){var t=this;if(this._config.showButton){this._container.style.width=this._container.style.height=this._config.buttonSize,this._container.style.borderRadius=this._config.borderRadius,this._container.addEventListener(\\\"click\\\",function(){t.getCurrentPosition()});var e=document.createElement(\\\"img\\\");e.style.display=\\\"none\\\",e.src=\\\"https://a.amap.com/jsapi/static/image/plugin/waite.png\\\",this._container.appendChild(e),this.waite=e}else this._container.style.display=\\\"none\\\"}},{key:\\\"getCurrentPosition\\\",value:function(t){t&&this._locateCallBacks.push(t),this._locating||this._locate()}},{key:\\\"_locate\\\",value:(e=d(g.mark(function t(){var e,n,o,r,i,a,s,c,u,l,f,p,h,d;return g.wrap(function(t){for(;;)switch(t.prev=t.next){case 0:if(this._locating)return t.abrupt(\\\"return\\\");t.next=2;break;case 2:if(this._locating=!0,this._config.showButton&&(this.waite.style.display=\\\"block\\\"),e={message:\\\"\\\",status:1},this._config.useNative&&k)return t.next=8,(window.AMapAndroidLoc?G:A)(this);t.next=10;break;case 8:(n=t.sent).status?e.message+=n.message:e=n;case 10:if(!this._config.GeoLocationFirst){t.next=23;break}if(e.status&&this.useGeoLocation)return t.next=14,C({timeout:this._config.timeout,enableHighAccuracy:this._config.enableHighAccuracy,maximumAge:this._config.maximumAge});t.next=16;break;case 14:(o=t.sent).status?(e.message+=o.message,e.originMessage=o.originMessage):(o.message=e.message+o.message,e=o);case 16:if(e.status&&this.useIPLocation)return t.next=19,P();t.next=21;break;case 19:(r=t.sent).status?e.message+=r.message:(r.message=e.message+r.message,r.originMessage=e.originMessage,e=r);case 21:t.next=33;break;case 23:if(e.status&&this.useIPLocation)return t.next=26,P();t.next=28;break;case 26:(i=t.sent).status?e.message+=i.message:(i.message=e.message+i.message,e=i);case 28:if(e.status&&this.useGeoLocation)return t.next=31,C({timeout:this._config.timeout,enableHighAccuracy:this._config.enableHighAccuracy,maximumAge:this._config.maximumAge});t.next=33;break;case 31:(a=t.sent).status?(e.message+=a.message,e.originMessage=a.originMessage):(a.message=e.message+a.message,e=a);case 33:if(e.status&&this._config.getCityWhenFail)return t.next=36,S();t.next=38;break;case 36:(s=t.sent).status?e.message+=s.message:(s.message=e.message+s.message,e=s);case 38:if(e.status){t.next=59;break}if(!e.isConverted){t.next=43;break}e.message+=\\\"No need convert.\\\",t.next=48;break;case 43:return t.next=45,O(e.position);case 45:(c=t.sent).status||(e.position=c.location),e.message+=c.message;case 48:if(this._config.needAddress)return t.next=51,b(e,this._config.extensions);t.next=52;break;case 51:e=t.sent;case 52:for(this.showPosition(e.position,e.accuracy),u=0,l=this._locateCallBacks.length;u<l;u+=1)\\\"function\\\"==typeof this._locateCallBacks[u]&&this._locateCallBacks[u].call(this,\\\"complete\\\",e);for(this._locateCallBacks=[],f=0,p=this._watchCallBacks.length;f<p;f+=1)\\\"function\\\"==typeof this._watchCallBacks[f]&&this._watchCallBacks[f].call(this,\\\"complete\\\",e);this.emit(\\\"complete\\\",e),t.next=62;break;case 59:for(h=0,d=this._locateCallBacks.length;h<d;h+=1)\\\"function\\\"==typeof this._locateCallBacks[h]&&this._locateCallBacks[h].call(this,\\\"error\\\",e);this._locateCallBacks=[],this.emit(\\\"error\\\",e);case 62:this._locating=!1,this._config.showButton&&(this.waite.style.display=\\\"none\\\");case 64:case\\\"end\\\":return t.stop()}},t,this)})),function(){return e.apply(this,arguments)})},{key:\\\"getCityInfo\\\",value:function(e){e&&S().then(function(t){t.status?e(\\\"error\\\",t):e(\\\"complete\\\",t)})}},{key:\\\"_handleViewChange\\\",value:function(){}},{key:\\\"showPosition\\\",value:function(t,e){if(this.map){this.options;this._config.showMarker&&(this._marker||(this._marker=new s.Marker(this._config.markerOptions),this._marker.isOfficial=!0),this._marker.setPosition(t),this.map.add(this._marker)),this._config.showCircle&&(this._circle||(this._circle=new s.Circle(this._config.circleOptions)),this._circle.setCenter(t),this._circle.setRadius(e||0),this.map.add(this._circle),this._circle.isOfficial=!0),this._config.panToLocation&&this.map.panTo([t.lng,t.lat]),this._config.zoomToAccuracy&&this.map.setFitView(this._circle,!0,void 0,17)}}},{key:\\\"watchPosition\\\",value:function(t){var e=this;if(navigator.geolocation)return this._watchCallBacks.push(t),this.watchInterval||(this.watchInterval=setInterval(function(){e.getCurrentPosition()},1e4)),this.watchInterval;t(\\\"error\\\",{status:0,info:\\\"NOT_SUPPORT\\\",message:\\\"Browser not Support GeoLocation.\\\"})}},{key:\\\"clearWatch\\\",value:function(){clearInterval(this.watchInterval),this._watchCallBacks=[]}}]),r}(s.Control),W=function(t){f(n,t);var e=m(n);function n(){return i(this,n),e.call(this)}return a(n,[{key:\\\"getLocation\\\",value:function(t,e){var n=window.AMapAndroidLoc;this.on(\\\"get\\\",e,t,!0),n.getLocation(JSON.stringify({to:t._container.timeout,useGPS:1,watch:0,callback:\\\"AMap.Geolocation.cbk\\\"}))}},{key:\\\"stopLocation\\\",value:function(t){var e=window.AMapAndroidLoc;this.off(\\\"get\\\",cbk,t),e.stopLocation()}}]),n}(s.Event);W.instance=new W,U.cbk=function(t){t=JSON.parse(t);var e=W.instance;e.emit(\\\"get\\\",t),e.emit(\\\"watch\\\",t)},s.Geolocation=U}(window.AMap);\",\"css\":\"@-webkit-keyframes rotate{0%{-webkit-transform:rotate(0deg)}25%{-webkit-transform:rotate(90deg)}50%{-webkit-transform:rotate(180deg)}75%{-webkit-transform:rotate(270deg)}to{-webkit-transform:rotate(1turn)}}.amap-geolocation{bottom:15px;right:15px;background-color:#fff;height:32px;width:32px;border-radius:50%;box-shadow:0 0 5px silver;cursor:pointer;background-image:url(https://a.amap.com/jsapi/static/image/plugin/locate.png);background-size:24px;background-repeat:no-repeat;background-position:50%}.amap-geolocation img{height:24px;width:24px;background-color:#fff;margin:4px;border-radius:50%;-webkit-animation:rotate 2s linear infinite}.amap-geolocation-marker{width:19px;height:19px;border-radius:50%;box-shadow:0 0 8px #4d95ec;background-color:#fff;opacity:.9}\"}"}, {"name": "_AMap_AMap.ToolBar", "value": "{\"version\":\"1736748443937\",\"script\":\"(function(){\\\"use strict\\\";var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};function __extends(d,b){if(typeof b!==\\\"function\\\"&&b!==null)throw new TypeError(\\\"Class extends value \\\"+String(b)+\\\" is not a constructor or null\\\");extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}var _SuppressedError=typeof SuppressedError===\\\"function\\\"?SuppressedError:function(error,suppressed,message){var e=new Error(message);return e.name=\\\"SuppressedError\\\",e.error=error,e.suppressed=suppressed,e};var zoomRange=[2,26];var defultOpts={position:{bottom:\\\"20px\\\",right:\\\"20px\\\"},offset:[15,15]};var ToolBar=function(_super){__extends(ToolBar,_super);function ToolBar(opts){if(opts===void 0){opts=defultOpts}var _this=_super.call(this,opts)||this;_this._className=\\\"toolbar\\\";_this._handleZoomEnd=_this._handleZoomEnd.bind(_this);_this._config=AMap.extend(defultOpts,opts);_this.initContainer();_this._initUI();return _this}ToolBar.prototype.addTo=function(map){_super.prototype.addTo.call(this,map);this._handleZoomEnd();map.on(\\\"zoomend\\\",this._handleZoomEnd)};ToolBar.prototype.remove=function(){if(this.map){this.map.off(\\\"zoomend\\\",this._handleZoomEnd)}_super.prototype.remove.call(this)};ToolBar.prototype.removeFrom=function(map){if(map){map.off(\\\"zoomend\\\",this._handleZoomEnd)}_super.prototype.removeFrom.call(this,map)};ToolBar.prototype._handleZoomEnd=function(){var map=this.map;var zooms=map.getZooms();if(map.getZoom()<=zooms[0]){this._zoomOut.style.color=\\\"#ddd\\\"}else{this._zoomOut.style.color=\\\"#000\\\"}if(map.getZoom()>=zooms[1]){this._zoomIn.style.color=\\\"#ddd\\\"}else{this._zoomIn.style.color=\\\"#000\\\"}};ToolBar.prototype._initUI=function(){var _this=this;this._zoomIn=AMap.DomUtil.create(\\\"span\\\",this._container,\\\"amap-ctrl-zoomin\\\");this._zoomOut=AMap.DomUtil.create(\\\"span\\\",this._container,\\\"amap-ctrl-zoomout\\\");this._zoomIn.innerHTML=\\\"+\\\";this._zoomOut.innerHTML=\\\"-\\\";this._zoomIn.setAttribute(\\\"data-type\\\",\\\"add\\\");this._zoomOut.setAttribute(\\\"data-type\\\",\\\"sub\\\");this._container.addEventListener(\\\"click\\\",function(event){if(event.target instanceof HTMLElement){var type=event.target.getAttribute(\\\"data-type\\\");var z=_this.map.getZoom();if(type===\\\"add\\\"){_this.map.setZoom(Math.min(z+1,zoomRange[1]))}else if(type===\\\"sub\\\"){_this.map.setZoom(Math.max(z-1,2))}}})};return ToolBar}(AMap.Control);AMap[\\\"ToolBar\\\"]=ToolBar})(); \"}"}, {"name": "_AMap_AMap.Geocoder", "value": "{\"version\":\"1736748443937\",\"script\":\"!function(c){\\\"use strict\\\";function i(e){return(i=\\\"function\\\"==typeof Symbol&&\\\"symbol\\\"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&\\\"function\\\"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?\\\"symbol\\\":typeof e})(e)}function s(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,\\\"value\\\"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}function o(e){return(o=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function a(e,t){return(a=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function p(e,t){if(t&&(\\\"object\\\"==typeof t||\\\"function\\\"==typeof t))return t;if(void 0!==t)throw new TypeError(\\\"Derived constructors may only return object or undefined\\\");t=e;if(void 0===t)throw new ReferenceError(\\\"this hasn't been initialised - super() hasn't been called\\\");return t}function d(r){var n=function(){if(\\\"undefined\\\"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if(\\\"function\\\"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var e,t=o(r);return p(this,n?(e=o(this).constructor,Reflect.construct(t,arguments,e)):t.apply(this,arguments))}}var e=function(e){var t=o;if(\\\"function\\\"!=typeof e&&null!==e)throw new TypeError(\\\"Super expression must either be null or a function\\\");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,\\\"prototype\\\",{writable:!1}),e&&a(t,e);var r,n=d(o);function o(e){var t;if(this instanceof o)return(t=n.call(this)).CLASS_NAME=\\\"AMap.Geocoder\\\",t.opt=e||{},t.config=c.getConfig(),t.url=t.config.server+\\\"/v3/geocode\\\",t;throw new TypeError(\\\"Cannot call a class as a function\\\")}return t=o,(e=[{key:\\\"getLocation\\\",value:function(e,r){var t,n=this;e?(t={key:this.config.key,s:\\\"rsv3\\\",language:this.opt.lang||\\\"zh_cn\\\"},e instanceof Array&&(e=e.join(\\\"|\\\"),t.batch=!0),(t.address=e)instanceof Array&&(e=e.join(\\\"|\\\"),t.push(\\\"batch=true\\\")),this._mergeParams(this.opt,{city:\\\"city\\\"},t),e=this.url+\\\"/geo\\\",c.WebService.get(e,t,function(e,t){\\\"complete\\\"===e&&t.status&&\\\"1\\\"===t.status&&n._geocodingComplete(t,r),\\\"error\\\"===e&&n._onError(t,r)})):(this.emit(\\\"error\\\",{info:\\\"NO_PARAMS\\\"}),r&&\\\"function\\\"==typeof r&&r(\\\"error\\\",\\\"NO_PARAMS\\\"))}},{key:\\\"setCity\\\",value:function(e){this.opt.city=e}},{key:\\\"setLang\\\",value:function(e){this.opt.lang=e||\\\"zh_cn\\\"}},{key:\\\"getLang\\\",value:function(){return this.opt.lang}},{key:\\\"getAddress\\\",value:function(e,r){var n=this,t=(e=c.Util.parseLngLatData(e),[]),o={key:this.config.key,s:\\\"rsv3\\\",language:this.opt.lang||\\\"zh_cn\\\"};if(e instanceof Array){for(var i=0,s=e.length;i<s;i+=1)t.push(e[i]+\\\"\\\");t=t.join(\\\"|\\\"),o.batch=!0}else t=e.lng+\\\",\\\"+e.lat;o.location=t;this._mergeParams(this.opt,{radius:\\\"radius\\\",extensions:\\\"extensions\\\"},o);var a=this.url+\\\"/regeo\\\";c.WebService.get(a,o,function(e,t){\\\"complete\\\"===e&&t.status&&\\\"1\\\"===t.status?n._rgeocodingComplete(t,r):n._onError(t,r)})}},{key:\\\"_mergeParams\\\",value:function(e,t,r){for(var n in e)void 0!==e[n]&&void 0!==t[n]&&(r[t[n]]=e[n])}},{key:\\\"_str2LngLat\\\",value:function(e){return e?(e=e.split(\\\",\\\"),new c.LngLat(e[0],e[1])):null}},{key:\\\"_parseString\\\",value:function(e){return\\\"object\\\"===i(e)||void 0===e?\\\"\\\":e}},{key:\\\"_parseInt\\\",value:function(e){e=parseInt(e,10);return isNaN(e)?0:e}},{key:\\\"_geocodingComplete\\\",value:function(e,t){var r;if(parseInt(e.status,10))if(r={info:e.info,resultNum:e.count,geocodes:[]},e.geocodes.length){for(var n=0,o=e.geocodes.length;n<o;n+=1){var i=e.geocodes[n],i=0==i.location.length?null:{location:this._str2LngLat(i.location),formattedAddress:this._parseString(i.formatted_address),adcode:this._parseString(i.adcode),level:this._parseString(i.level),addressComponent:{neighborhood:this._parseString(i.neighborhood.name),neighborhoodType:this._parseString(i.neighborhood.type),building:this._parseString(i.building.name),buildingType:this._parseString(i.building.type),province:this._parseString(i.province),city:this._parseString(i.city),district:this._parseString(i.district),township:this._parseString(i.township),citycode:this._parseString(i.citycode),street:this._parseString(i.street),streetNumber:this._parseString(i.number)}};r.geocodes.push(i)}this.emit(\\\"complete\\\",r),t&&\\\"function\\\"==typeof t&&t(\\\"complete\\\",r)}else r.info=\\\"NO_DATA\\\",this.emit(\\\"complete\\\",r),t&&\\\"function\\\"==typeof t&&t(\\\"no_data\\\",{});else r={info:e.info},this.emit(\\\"error\\\",{result:r}),t&&\\\"function\\\"==typeof t&&t(\\\"error\\\",e.info)}},{key:\\\"parseBusinessAreas\\\",value:function(e){var t=[];if(e)for(var r=0;r<e.length;r+=1)e[r].id&&t.push({name:this._parseString(e[r].name),id:this._parseString(e[r].id),location:this._str2LngLat(e[r].location)});return t}},{key:\\\"_buildRegeocodes\\\",value:function(e){var t=[];if(e)for(var r=0,n=e.length;r<n;r+=1){var o=e[r];t.push(this._buildRegeocode(o))}return t}},{key:\\\"_buildRegeocode\\\",value:function(e){var t,r={addressComponent:{citycode:this._parseString(e.addressComponent.citycode),adcode:this._parseString(e.addressComponent.adcode),businessAreas:this.parseBusinessAreas(e.addressComponent.businessAreas),neighborhoodType:this._parseString(e.addressComponent.neighborhood.type),neighborhood:this._parseString(e.addressComponent.neighborhood.name),building:this._parseString(e.addressComponent.building.name),buildingType:this._parseString(e.addressComponent.building.type),street:this._parseString(e.addressComponent.streetNumber.street),streetNumber:this._parseString(e.addressComponent.streetNumber.number),province:this._parseString(e.addressComponent.province),city:this._parseString(e.addressComponent.city),district:this._parseString(e.addressComponent.district),towncode:this._parseString(e.addressComponent.towncode),township:this._parseString(e.addressComponent.township)},formattedAddress:this._parseString(e.formatted_address),roads:[],crosses:[],pois:[]};return e.roads&&(r.roads=this._parseRoads(e.roads)),e.roadinters&&(t=e.roadinters,this._isArray(t)||(t=[e.roadinters.roadinter]),r.crosses=this._parseCross(t)),e.pois&&(r.pois=this._parsePOI(e.pois)),e.aois&&(r.aois=this._parseAOIs(e.aois)),r}},{key:\\\"_rgeocodingComplete\\\",value:function(e,t){var r;if(parseInt(e.status,10)){if(r={info:e.info},e.regeocode){if(\\\"\\\"===this._parseString(e.regeocode.formatted_address))return r.info=\\\"NO_DATA\\\",this.emit(\\\"complete\\\",r),void(t&&\\\"function\\\"==typeof t&&t(\\\"no_data\\\",{}));r.regeocode=this._buildRegeocode(e.regeocode)}else e.regeocodes&&(r.regeocodes=this._buildRegeocodes(e.regeocodes));this.emit(\\\"complete\\\",r),t&&\\\"function\\\"==typeof t&&t(\\\"complete\\\",r)}else r={info:e.info},this.emit(\\\"error\\\",{result:r}),t&&\\\"function\\\"==typeof t&&t(\\\"error\\\",e.info)}},{key:\\\"_parsePOI\\\",value:function(e){for(var t=[],r=0;r<e.length;r+=1){var n=e[r];t[r]={id:n.id,name:n.name,type:n.type,address:this._parseString(n.address),distance:this._parseInt(n.distance),tel:this._parseString(n.tel),location:this._str2LngLat(n.location),direction:this._parseString(n.direction),businessArea:this._parseString(n.businessarea)}}return t}},{key:\\\"_parseAOIs\\\",value:function(e){for(var t=[],r=0,n=e.length;r<n;r++){var o=e[r];t.push({adcode:this._parseString(o.adcode),area:this._parseString(o.area),id:this._parseString(o.id),location:this._str2LngLat(o.location),name:this._parseString(o.name),type:this._parseString(o.type)})}return t}},{key:\\\"_isArray\\\",value:function(e){return\\\"[object Array]\\\"===Object.prototype.toString.call(e)}},{key:\\\"_parseCross\\\",value:function(e){for(var t=[],r=0;r<e.length;r+=1){var n=e[r];t[r]={distance:this._parseInt(n.distance),direction:this._parseString(n.direction),first_id:this._parseString(n.first_id),first_name:this._parseString(n.first_name),second_name:this._parseString(n.second_name),second_id:this._parseString(n.second_id),location:this._str2LngLat(n.location)}}return t}},{key:\\\"_parseRoads\\\",value:function(e){for(var t=[],r=0;r<e.length;r+=1){var n=e[r];t[r]={id:n.id,name:n.name,distance:this._parseInt(n.distance),direction:this._parseString(n.direction),location:this._str2LngLat(n.location)}}return t}},{key:\\\"_onError\\\",value:function(e,t){this.emit(\\\"error\\\",{e:e}),t&&\\\"function\\\"==typeof t&&t(\\\"error\\\",e.info)}}])&&s(t.prototype,e),r&&s(t,r),Object.defineProperty(t,\\\"prototype\\\",{writable:!1}),o}(c.Event);c.Geocoder=e}(window.AMap); \"}"}, {"name": "_AMap_AMap.PlaceSearch", "value": "{\"version\":\"1736748443937\",\"script\":\"!function(AMap){\\\"use strict\\\";function _typeof(obj){return(_typeof=\\\"function\\\"==typeof Symbol&&\\\"symbol\\\"==typeof Symbol.iterator?function(obj){return typeof obj}:function(obj){return obj&&\\\"function\\\"==typeof Symbol&&obj.constructor===Symbol&&obj!==Symbol.prototype?\\\"symbol\\\":typeof obj})(obj)}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor))throw new TypeError(\\\"Cannot call a class as a function\\\")}function _defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||!1,descriptor.configurable=!0,\\\"value\\\"in descriptor&&(descriptor.writable=!0),Object.defineProperty(target,descriptor.key,descriptor)}}function _createClass(Constructor,protoProps,staticProps){protoProps&&_defineProperties(Constructor.prototype,protoProps),staticProps&&_defineProperties(Constructor,staticProps),Object.defineProperty(Constructor,\\\"prototype\\\",{writable:!1})}function _inherits(subClass,superClass){if(\\\"function\\\"!=typeof superClass&&null!==superClass)throw new TypeError(\\\"Super expression must either be null or a function\\\");subClass.prototype=Object.create(superClass&&superClass.prototype,{constructor:{value:subClass,writable:!0,configurable:!0}}),Object.defineProperty(subClass,\\\"prototype\\\",{writable:!1}),superClass&&_setPrototypeOf(subClass,superClass)}function _getPrototypeOf(o){return(_getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(o){return o.__proto__||Object.getPrototypeOf(o)})(o)}function _setPrototypeOf(o,p){return(_setPrototypeOf=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(o,p){return o.__proto__=p,o})(o,p)}function _assertThisInitialized(self){if(void 0===self)throw new ReferenceError(\\\"this hasn't been initialised - super() hasn't been called\\\");return self}function _createSuper(Derived){var hasNativeReflectConstruct=function(){if(\\\"undefined\\\"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if(\\\"function\\\"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}();return function(){var NewTarget,Super=_getPrototypeOf(Derived),Super=(NewTarget=hasNativeReflectConstruct?(NewTarget=_getPrototypeOf(this).constructor,Reflect.construct(Super,arguments,NewTarget)):Super.apply(this,arguments),this);if(NewTarget&&(\\\"object\\\"==typeof NewTarget||\\\"function\\\"==typeof NewTarget))return NewTarget;if(void 0!==NewTarget)throw new TypeError(\\\"Derived constructors may only return object or undefined\\\");return _assertThisInitialized(Super)}}function _arrayLikeToArray(arr,len){(null==len||len>arr.length)&&(len=arr.length);for(var i=0,arr2=new Array(len);i<len;i++)arr2[i]=arr[i];return arr2}function _createForOfIteratorHelper(o,allowArrayLike){var i,it=\\\"undefined\\\"!=typeof Symbol&&o[Symbol.iterator]||o[\\\"@@iterator\\\"];if(!it){if(Array.isArray(o)||(it=function(o,minLen){if(o){if(\\\"string\\\"==typeof o)return _arrayLikeToArray(o,minLen);var n=Object.prototype.toString.call(o).slice(8,-1);return\\\"Map\\\"===(n=\\\"Object\\\"===n&&o.constructor?o.constructor.name:n)||\\\"Set\\\"===n?Array.from(o):\\\"Arguments\\\"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(o,minLen):void 0}}(o))||allowArrayLike&&o&&\\\"number\\\"==typeof o.length)return it&&(o=it),i=0,{s:allowArrayLike=function(){},n:function(){return i>=o.length?{done:!0}:{done:!1,value:o[i++]}},e:function(e){throw e},f:allowArrayLike};throw new TypeError(\\\"Invalid attempt to iterate non-iterable instance.\\\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\\\")}var err,normalCompletion=!0,didErr=!1;return{s:function(){it=it.call(o)},n:function(){var step=it.next();return normalCompletion=step.done,step},e:function(e){didErr=!0,err=e},f:function(){try{normalCompletion||null==it.return||it.return()}finally{if(didErr)throw err}}}}var PlaceSearchRenderUtil={getElementsByClassName:function(className,tag,parent){for(var current,testClass=new RegExp(\\\"(^|\\\\\\\\s)\\\"+className+\\\"(\\\\\\\\s|$)\\\"),elements=(tag=tag||\\\"*\\\",(parent=parent||document).getElementsByTagName(tag)),returnElements=[],i=0;i<elements.length;i++)current=elements[i],testClass.test(current.className)&&returnElements.push(current);return returnElements},escapeHtml:function(text){var map={\\\"&\\\":\\\"&amp;\\\",\\\"<\\\":\\\"&lt;\\\",\\\">\\\":\\\"&gt;\\\",'\\\"':\\\"&quot;\\\",\\\"'\\\":\\\"&#039;\\\"};return text.replace(/[&<>\\\"']/g,function(m){return map[m]})},trimStr:function(str){return str.replace(/^[\\\\s\\\\uFEFF\\\\xA0]+|[\\\\s\\\\uFEFF\\\\xA0]+$/g,\\\"\\\")},simpleChildPoiName:function(name,parentPoi){var parentPoi=this.trimStr(parentPoi.name),match=name.match(/^(.*?)\\\\((.*?)\\\\)$/);return match?match[1]===parentPoi?match[2]:match[1].replace(parentPoi,\\\"\\\"):name.replace(parentPoi,\\\"\\\")}},PlaceSearchRenderListener=function(){function PlaceSearchRenderListener(placeSearchRender){_classCallCheck(this,PlaceSearchRenderListener),this._lastEventObject=null,this._parent=placeSearchRender}return _createClass(PlaceSearchRenderListener,[{key:\\\"getTargetPOIBox\\\",value:function(elems,id){var _step,targetElem=null,_iterator=_createForOfIteratorHelper(elems);try{for(_iterator.s();!(_step=_iterator.n()).done;){var element=_step.value;if(id==element.getAttribute(\\\"data-idx\\\")){targetElem=element;break}}}catch(err){_iterator.e(err)}finally{_iterator.f()}return targetElem}},{key:\\\"markerClick\\\",value:function(){var lastIndex,element,params=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},panel=params.panel,data=params.data,position=params.position,marker=params.marker,placeSearchInstance=params.placeSearchInstance,_params$noMarkerClick=params.noMarkerClickEvent,_params$noMarkerClick=void 0!==_params$noMarkerClick&&_params$noMarkerClick,params=params.e,parent=this._parent,oldContent=parent.infoWindow.getContent(),oldContent=(!oldContent||(oldContent=parent.util.getElementsByClassName(\\\"poi-more\\\",\\\"a\\\",oldContent)[0])&&(oldContent._data=null,oldContent.clickHandler,oldContent.clickHandler=null),parent.view.createInfowindowContent(data)),close=oldContent.querySelector(\\\".amap-combo-close\\\"),detail=(close&&(close.addEventListener(\\\"touchend\\\",function(e){parent.infoWindow.close()},this),close.addEventListener(\\\"click\\\",function(e){parent.infoWindow.close()},this)),parent.util.getElementsByClassName(\\\"poi-more\\\",\\\"a\\\",oldContent)[0]),close=(detail&&(detail._data=data,detail.clickHandler=detail.addEventListener(\\\"click\\\",function(e){parent.onDetailClick.call(parent,detail)})),parent.infoWindow&&(parent.infoWindow.setContent(oldContent),parent.infoWindow.setOffset(data._isChildPoi?new AMap.Pixel(0,-3):new AMap.Pixel(0,-25)),parent.infoWindow.open(parent.options.map,position)),parent.options.map.setCenter(position),data._idx),position=(this.last&&(this.last.className=\\\"poibox\\\",lastIndex=parseInt(this.last.getAttribute(\\\"data-idx\\\"),10)),panel&&(oldContent=panel.querySelectorAll(\\\".poibox\\\"),element=this.getTargetPOIBox(oldContent,close),this._currentDiv=element),this._currentIndex=parseInt(close,10),this._currentDiv);position&&(position.className=\\\"poibox active\\\"),this.last=position,!_params$noMarkerClick&&placeSearchInstance&&(panel={type:\\\"markerClick\\\",id:(data=marker._data).id,marker:marker,listElement:position,data:data,event:params,index:this._currentIndex},placeSearchInstance.emit(\\\"markerClick\\\",panel),(element&&void 0===lastIndex||lastIndex!==this._currentIndex)&&(oldContent={id:data.id,marker:marker,listElement:position,data:data,event:params,index:this._currentIndex},this._lastEventObject&&delete this._lastEventObject.type,placeSearchInstance.emit(\\\"selectChanged\\\",{type:\\\"selectChanged\\\",selected:oldContent,lastSelected:this._lastEventObject})),this._lastEventObject=panel)}},{key:\\\"unfocusTitleClick\\\",value:function(e,params){var lastIndex,dataIndex=params.dataIndex,marker=params.marker,placeSearchInstance=params.placeSearchInstance,params=params.element,parent=this._parent,div=(this.last&&(this.last.className=\\\"poibox\\\",lastIndex=parseInt(this.last.getAttribute(\\\"data-idx\\\"),10)),this._currentDiv=params,this._currentIndex=parseInt(dataIndex,10),this._currentDiv);if(div&&(div.className=\\\"poibox active\\\"),this.last=div,parent.options.map){for(var strIdx,target=e.target||e.srcElement;target&&target!==div&&!(strIdx=target.getAttribute(\\\"data-idx\\\"));)target=target.parentNode;parent.setCenter(strIdx?parseInt(strIdx,10):this._currentIndex)}placeSearchInstance&&(parent={type:params=\\\"listElementClick\\\",id:(dataIndex=marker._data).id,marker:marker,listElement:div,data:dataIndex,event:e,index:this._currentIndex},placeSearchInstance.emit(params,parent),void 0!==lastIndex&&lastIndex===this._currentIndex||(params={id:dataIndex.id,marker:marker,listElement:div,data:dataIndex,event:e,index:this._currentIndex},this._lastEventObject&&delete this._lastEventObject.type,placeSearchInstance.emit(\\\"selectChanged\\\",{type:\\\"selectChanged\\\",selected:params,lastSelected:this._lastEventObject})),this._lastEventObject=parent)}}]),PlaceSearchRenderListener}(),PlaceSearchRenderView={createInfowindowContent:function(data){var content=document.createElement(\\\"div\\\"),div=document.createElement(\\\"div\\\"),c=(div.className=\\\"amap-content-body\\\",[]),data=(c.push('<div class=\\\"amap-lib-infowindow\\\">'),c.push('    <div class=\\\"amap-lib-infowindow-title\\\">'+(data._isChildPoi?\\\"\\\":data.index+1+\\\".\\\")+data.name+\\\"</div>\\\"),c.push('    <div class=\\\"amap-lib-infowindow-content\\\">'),c.push('        <div class=\\\"amap-lib-infowindow-content-wrap\\\">'),c.push(\\\"             <div>地址：\\\"+data.address+\\\"</div>\\\"),data.tel&&c.push(\\\"             <div>电话：\\\"+data.tel+\\\"</div>\\\"),c.push(\\\"        </div>\\\"),c.push(\\\"    </div>\\\"),c.push(\\\"</div>\\\"),div.innerHTML=c.join(\\\"\\\"),document.createElement(\\\"div\\\")),c=(data.className=\\\"amap-combo-sharp\\\",div.appendChild(data),document.createElement(\\\"div\\\"));return c.className=\\\"amap-combo-close\\\",div.appendChild(c),c.href=\\\"javascript: void(0)\\\",content.appendChild(div),content.appendChild(c),content.appendChild(data),content},getChildrenHtml:function(children,parentPoi){var parts=[];parts.push('<div class=\\\"poi-children-box\\\">'),parts.push('<ul class=\\\"poi-children-list\\\">');for(var i=0,len=children.length;i<len;i++){var childPoi=children[i],name=PlaceSearchRenderUtil.trimStr(childPoi.name),name=PlaceSearchRenderUtil.escapeHtml(PlaceSearchRenderUtil.simpleChildPoiName(name,parentPoi));parts.push('<li class=\\\"poi-child-item\\\" data-idx=\\\"'+childPoi._idx+'\\\" title=\\\"'+name+'\\\">'),parts.push(name),parts.push(\\\"</li>\\\")}return parts.push(\\\"</ul>\\\"),parts.push(\\\"</div>\\\"),parts.join(\\\"\\\")},createPanel:function(result){if(!(result.poiList&&0<result.poiList.pois.length&&\\\"NO_DATA\\\"!==result.info))return\\\"<div class='amap_lib_placeSearch'>抱歉，未搜索到有效的结果。</div>\\\";var pois=result.poiList.pois,c=[];c.push('<div class=\\\"amap_lib_placeSearch\\\">'),c.push('    <div class=\\\"amap_lib_placeSearch_list\\\">'),c.push(\\\"        <ul>\\\");for(var i=0;i<pois.length;i++){var poi,pic=(poi=pois[i]).photos&&poi.photos[0].url,pic='<img src=\\\"'+(pic||\\\"http://a.amap.com/jsapi_demos/static/images/search-img-default.png\\\")+'\\\">';c.push('            <li class=\\\"poibox\\\" data-idx=\\\"'+poi._idx+'\\\">'),c.push('                <div class=\\\"amap_lib_placeSearch_pic\\\">'+pic+\\\"</div>\\\"),c.push('                <h3 class=\\\"poi-title\\\">'),c.push('                \\\\t<span class=\\\"poi-name amap-ellipsis\\\">'+poi.name+\\\"</span>\\\"),c.push(\\\"                </h3>\\\"),c.push('                <div class=\\\"poi-info\\\">'),c.push('                \\\\t<p class=\\\"poi-addr amap-ellipsis\\\">地址：'+poi.address+\\\"</p>\\\"),poi.tel&&c.push('                <p class=\\\"poi-tel amap-ellipsis\\\">电话：'+poi.tel+\\\"</p>\\\"),c.push(\\\"                </div>\\\"),poi.children&&c.push(this.getChildrenHtml(poi.children,poi)),c.push(\\\"            </li>\\\")}c.push(\\\"        </ul>\\\"),c.push(\\\"    </div>\\\"),c.push('    <div class=\\\"amap_lib_placeSearch_page\\\">'),c.push(\\\"        <div>\\\"),c.push(\\\"            <p>\\\");var result=result.poiList,count=result.count,pageIndex=result.pageIndex,result=result.pageSize,count=Math.ceil(count/result);return 3<pageIndex&&c.push(this.createPageButton(1,\\\"首页\\\")),1<pageIndex&&c.push(this.createPageButton(pageIndex-1,\\\"上一页\\\")),1<=pageIndex-2&&c.push(this.createPageButton(pageIndex-2,pageIndex-2)),1<=pageIndex-1&&c.push(this.createPageButton(pageIndex-1,pageIndex-1)),c.push('                <span class=\\\"current\\\">'+pageIndex+\\\"</span>\\\"),pageIndex+1<=count&&c.push(this.createPageButton(pageIndex+1,pageIndex+1)),pageIndex+2<=count&&c.push(this.createPageButton(pageIndex+2,pageIndex+2)),pageIndex<count&&c.push(this.createPageButton(pageIndex+1,\\\"下一页\\\")),c.push(\\\"            </p>\\\"),c.push(\\\"        </div>\\\"),c.push(\\\"    </div>\\\"),c.push(\\\"</div>\\\"),c.join(\\\"\\\")},createPageButton:function(pageNum,text){return\\\"<span><a pageNo=\\\"+pageNum+' class=\\\"pageLink\\\" >'+text+\\\"</a></span>\\\"}},PlaceSearchRender=function(){_inherits(PlaceSearchRender,AMap.Event);var _super=_createSuper(PlaceSearchRender);function PlaceSearchRender(showCover){var _this;return _classCallCheck(this,PlaceSearchRender),(_this=_super.call(this)).CLASS_NAME=\\\"AMap.PlaceSearchRender\\\",_this.options={},_this.circleOptions={id:\\\"place-search-circle\\\",radius:3e3,strokeColor:\\\"#72ccff\\\",strokeOpacity:.8,strokeWeight:1,fillColor:\\\"#d0e7f8\\\",fillOpacity:.2,bubble:!0},_this.boundOptions={id:\\\"place-search-bound\\\",strokeColor:\\\"#72ccff\\\",strokeOpacity:.8,strokeWeight:1,fillColor:\\\"#d0e7f8\\\",fillOpacity:.2,bubble:!0},_this.searchCircle=null,_this.searchBound=null,_this.util=PlaceSearchRenderUtil,_this.listener=new PlaceSearchRenderListener(_assertThisInitialized(_this)),_this.view=PlaceSearchRenderView,_this.ifShowCover=showCover,_this}return _createClass(PlaceSearchRender,[{key:\\\"autoRender\\\",value:function(options){this.clear(),options&&options.methodName&&options.methodArgumments&&(options.panel||options.map)&&(this.options=options,this.callback(\\\"complete\\\",options.data))}},{key:\\\"addIdxField\\\",value:function(pois){for(var stack=[].concat(pois),idx=0;;){var poi=stack.shift();if(!poi)break;if(poi._idx=idx++,poi.index=poi._idx,poi.id=this.util.trimStr(poi.id),poi.children)for(var i=0,len=poi.children.length;i<len;i++)poi.children[i]._isChildPoi=!0,poi.children[i]._parentIdx=poi._idx,poi.children[i].citycode=poi.citycode,stack.push(poi.children[i])}}},{key:\\\"callback\\\",value:function(status,result){var _this2=this,options=(this.clear(),this.options);if((options.callback&&options.callback(status,result),\\\"complete\\\"===status)&&((this.result=result).poiList&&this.addIdxField(result.poiList.pois),options.map&&(this.infoWindow=new AMap.InfoWindow({size:new AMap.Size(0,0),isCustom:!0,offset:[0,-25]}),this._overlays=[],this._highlightOverlay=null,(result.cityList||result.keywordList||result.poiList)&&this.drawOverlays(result),\\\"searchNearBy\\\"===options.methodName&&this.ifShowCover&&(status=this.options.methodArgumments,this.drawCircle(status[1],status[2])),\\\"searchInBounds\\\"===options.methodName&&this.ifShowCover&&(status=this.options.methodArgumments,this.drawBound(status[1]))),options.panel&&(\\\"[object String]\\\"===Object.prototype.toString.call(options.panel)&&(options.panel=document.getElementById(options.panel)),options.panel.innerHTML=this.view.createPanel(result),this.enableListeners(),options.placeSearchInstance)))for(var details=this.util.getElementsByClassName(\\\"poi-more\\\",\\\"a\\\",options.panel),i=0;i<details.length;i+=1)!function(i){var a=details[i];a.dIndex=i,a.clickHandler=a.addEventListener(\\\"click\\\",function(e){_this2.onDetailClick.call(_this2,a)})}(i)}},{key:\\\"enableListeners\\\",value:function(){for(var unfocusTitle,_this3=this,listener=this.listener,unfocusTitles=this.util.getElementsByClassName(\\\"poibox\\\",\\\"*\\\",this.options.panel),i=0;i<unfocusTitles.length;i++)!function(i,_unfocusTitle){(_unfocusTitle=unfocusTitles[i]).addEventListener(\\\"click\\\",function(e){var index=_unfocusTitle.getAttribute(\\\"data-idx\\\"),placeSearchInstance=_this3.options.placeSearchInstance,marker=_this3._overlays[index];marker&&(index={element:_unfocusTitle,dataIndex:index,marker:marker,placeSearchInstance:placeSearchInstance},listener.unfocusTitleClick.call(listener,e,index))}),unfocusTitle=_unfocusTitle}(i,unfocusTitle);for(var pageLink,pageLinks=this.util.getElementsByClassName(\\\"pageLink\\\",\\\"*\\\",this.options.panel),_i=0;_i<pageLinks.length;_i++)!function(_pageLink,_i){(_pageLink=pageLinks[_i]).addEventListener(\\\"click\\\",function(){var pageNo=_pageLink.getAttribute(\\\"pageNo\\\");_this3.toPage(pageNo)}),pageLink=_pageLink}(pageLink,_i)}},{key:\\\"onDetailClick\\\",value:function(detail){this.options.placeSearchInstance.detailOnAMAP(detail._data||this.options.data.poiList.pois[detail.dIndex])}},{key:\\\"clear\\\",value:function(){this.clearPanel(),this.clearOverlays(),this.clearCircle(),this.clearBound()}},{key:\\\"drawOverlays\\\",value:function(result){this.clearOverlays();result=result.poiList.pois;this._overlays=this.addOverlays(result),this.options.autoFitView&&this.options.map.setFitView(this._overlays,!0)}},{key:\\\"addChildrenOverlays\\\",value:function(children,overlays){for(var _this4=this,i=0,len=children.length;i<len;i++)!function(i){var i=children[i],extraClass=\\\"\\\";switch(i.subtype){case\\\"门\\\":extraClass=\\\"gate\\\";break;case\\\"停车场\\\":extraClass=\\\"parking\\\"}var point=new AMap.Marker({map:_this4.options.map,zIndex:100,offset:new AMap.Pixel(-6,-6),size:new AMap.Size(12,12),topWhenClick:!0,position:i.location,content:'<div class=\\\"amap_lib_placeSearch_child_poi '+extraClass+'\\\">'+i._idx+\\\"</div>\\\"});point.isOfficial=!0,point._data=i,point.on(\\\"click\\\",function(e){e={data:point._data,position:point.getPosition(),marker:point,placeSearchInstance:_this4.options.placeSearchInstance,panel:_this4.options.panel,e:e};_this4.listener.markerClick(e)}),overlays[i._idx]=point}(i)}},{key:\\\"addOverlays\\\",value:function(points){for(var point,_this5=this,map=this.options.map,_overlays=[],children=[],i=0;i<points.length;i++)!function(i,_point){(_point=new AMap.Marker({map:map,zIndex:101,offset:new AMap.Pixel(-9,-31),size:new AMap.Size(19,33),topWhenClick:!0,position:points[i].location,content:'<div class=\\\"amap_lib_placeSearch_poi\\\"></div>'})).isOfficial=!0,_point._data=points[i],_point.on(\\\"click\\\",function(e){e={data:_point._data,position:_point.getPosition(),marker:_point,placeSearchInstance:_this5.options.placeSearchInstance,panel:_this5.options.panel,e:e};_this5.listener.markerClick(e)}),_overlays[points[i]._idx]=_point,points[i].children&&children.push.apply(children,points[i].children),point=_point}(i,point);return this.addChildrenOverlays(children,_overlays),_overlays}},{key:\\\"toggleChildrenOverlays\\\",value:function(idx,show){if(!isNaN(idx)){var point=this._overlays[idx];if(point){var poi=point._data,map=this.options.map;if(poi.children){for(var overlays=[point],i=0,len=poi.children.length;i<len;i++){var childOverlay=this._overlays[poi.children[i]._idx];childOverlay&&(childOverlay.setMap(show?map:null),overlays.push(childOverlay))}this.options.autoFitView&&this.options.map.setFitView(overlays,!0)}}else console.warn(\\\"Missing marker: \\\",idx)}}},{key:\\\"clearPanel\\\",value:function(){if(this.options&&this.options.panel instanceof HTMLDivElement){for(var details=this.util.getElementsByClassName(\\\"poi-more\\\",\\\"a\\\",this.options.panel),i=0;i<details.length;i+=1){var a=details[i];a.clickHandler,a.clickHandler=null}this.options.panel.innerHTML=\\\"\\\"}}},{key:\\\"clearOverlays\\\",value:function(){if(this._overlays){for(var i=0;i<this._overlays.length;i++)this._overlays[i].setMap(null);this._overlays=[]}var oldContent;this.infoWindow&&((oldContent=this.infoWindow.getContent())&&(oldContent=this.util.getElementsByClassName(\\\"poi-more\\\",\\\"a\\\",oldContent)[0])&&(oldContent._data=null,oldContent.clickHandler&&(oldContent.clickHandler=null)),this.infoWindow.close())}},{key:\\\"setCenter\\\",value:function(index){var poi,point=this._overlays[index];point?(poi=point._data,this.options.map.setCenter(poi.location),point.setTop(!0),poi={data:point._data,position:point.getPosition(),marker:point,placeSearchInstance:this.options.placeSearchInstance,panel:this.options.panel,noMarkerClickEvent:!0},this.listener.markerClick(poi)):console.warn(\\\"Missing marker: \\\",index)}},{key:\\\"toPage\\\",value:function(pageNo){pageNo.length&&(pageNo=parseInt(pageNo,10)),this.options.placeSearchInstance.setPageIndex(pageNo),this.options.placeSearchInstance[this.options.methodName].apply(this.options.placeSearchInstance,this.options.methodArgumments)}},{key:\\\"drawCircle\\\",value:function(center,radius){this.clearCircle(),this.circleOptions.map=this.options.map,this.circleOptions.center=center,this.circleOptions.radius=radius,this.searchCircle=new AMap.Circle(this.circleOptions),this.options.map.add(this.searchCircle),this.searchCircle.isOfficial=!0}},{key:\\\"clearCircle\\\",value:function(){this.searchCircle&&(this.searchCircle.setMap(null),this.searchCircle=null)}},{key:\\\"drawBound\\\",value:function(bound){this.clearBound();var path=[],bound=(bound.getNorthWest?path.push(bound.getNorthWest(),bound.getNorthEast(),bound.getSouthEast(),bound.getSouthWest()):path=bound.getPath(),this.boundOptions.path=path,this.boundOptions.map=this.options.map,new AMap.Polygon(this.boundOptions));bound.isOfficial=!0,this.searchBound=bound}},{key:\\\"clearBound\\\",value:function(){this.searchBound&&(this.searchBound.setMap(null),this.searchBound=null)}}]),PlaceSearchRender}(),PlaceSearch=function(){_inherits(PlaceSearch,AMap.Event);var _super=_createSuper(PlaceSearch);function PlaceSearch(opt){_classCallCheck(this,PlaceSearch),(_this=_super.call(this)).CLASS_NAME=\\\"AMap.PlaceSearch\\\",_this.opt={},_this.closed=!1,_this.opt=AMap.extend({},{showCover:!0,autoFitView:!0},opt||{});var _this,opt=_this.config=AMap.getConfig();return _this.opt.pageIndex=_this.opt.pageIndex||1,_this.opt.pageSize=\\\"number\\\"!=typeof _this.opt.pageSize||_this.opt.pageSize<0?10:50<_this.opt.pageSize?50:_this.opt.pageSize,_this.url=opt.server+\\\"/v3/place\\\",_this.renderEngine=PlaceSearchRender,(_this.opt.map||_this.opt.panel)&&(_this.render=new _this.renderEngine(_this.opt.showCover),_this.opt.extensions=\\\"all\\\"),_this.opt.children&&(_this.opt.extensions=\\\"all\\\"),_this}return _createClass(PlaceSearch,[{key:\\\"clear\\\",value:function(){this.render&&this.render.clear()}},{key:\\\"setLang\\\",value:function(lang){this.opt.lang=lang||\\\"zh_cn\\\"}},{key:\\\"getLang\\\",value:function(){return this.opt.lang||\\\"zh_cn\\\"}},{key:\\\"search\\\",value:function(keyword,cbk){var opts=this.opt,opts={city:opts.city,sortrule:opts.rankBy,types:opts.type,page:opts.pageIndex,offset:opts.pageSize,extensions:opts.extensions,citylimit:opts.citylimit,language:this.getLang(),s:\\\"rsv3\\\",children:this.opt.children?\\\"1\\\":\\\"\\\"};this._filterData(opts),this._query(this.url+\\\"/text\\\",opts,cbk,{keywords:keyword||\\\"\\\"},\\\"KEYWORD\\\",arguments)}},{key:\\\"searchInBounds\\\",value:function(keyword,bounds,cbk){var opts;bounds?(opts={city:(opts=this.opt).city,sortrule:opts.rankBy,types:opts.type,page:opts.pageIndex,offset:opts.pageSize,extensions:opts.extensions,citylimit:opts.citylimit,language:this.getLang(),polygon:bounds,s:\\\"rsv3\\\",children:this.opt.children?\\\"1\\\":\\\"\\\"},this._filterData(opts),this._query(this.url+\\\"/polygon\\\",opts,cbk,{keywords:keyword||\\\"\\\"},\\\"POLYGON\\\",arguments)):(this.emit(\\\"error\\\",{info:\\\"NO_PARAMS\\\"}),cbk&&\\\"function\\\"==typeof cbk&&cbk(\\\"error\\\",\\\"NO_PARAMS\\\"))}},{key:\\\"searchNearBy\\\",value:function(keyword,center,radius,cbk){var opts;center?(center=AMap.Util.parseLngLatData(center),opts={city:(opts=this.opt).city,sortrule:opts.rankBy,types:opts.type,page:opts.pageIndex,offset:opts.pageSize,extensions:opts.extensions,citylimit:opts.citylimit,language:this.getLang(),location:center.toString(),radius:radius,s:\\\"rsv3\\\",children:this.opt.children?\\\"1\\\":\\\"\\\"},this._filterData(opts),this._query(this.url+\\\"/around\\\",opts,cbk,{keywords:keyword||\\\"\\\"},\\\"NEARBY\\\",arguments)):(this.emit(\\\"error\\\",{info:\\\"NO_PARAMS\\\"}),cbk&&\\\"function\\\"==typeof cbk&&cbk(\\\"error\\\",\\\"NO_PARAMS\\\"))}},{key:\\\"getDetails\\\",value:function(PGUID,cbk){PGUID?(this.opt,this._query(this.url+\\\"/detail\\\",{id:PGUID,s:\\\"rsv3\\\"},cbk,{},\\\"ID\\\")):(this.emit(\\\"error\\\",{info:\\\"NO_PARAMS\\\"}),cbk&&\\\"function\\\"==typeof cbk&&cbk(\\\"error\\\",\\\"NO_PARAMS\\\"))}},{key:\\\"setType\\\",value:function(type){this.opt.type=type}},{key:\\\"setPageIndex\\\",value:function(pageIndex){this.opt.pageIndex=pageIndex||1}},{key:\\\"setPageSize\\\",value:function(pageSize){this.opt.pageSize=\\\"number\\\"!=typeof pageSize||pageSize<0?10:50<pageSize?50:pageSize}},{key:\\\"setCity\\\",value:function(city){this.opt.city=city}},{key:\\\"setCityLimit\\\",value:function(citylimit){this.opt.citylimit=citylimit}},{key:\\\"poiOnAMAP\\\",value:function(p){var pp={},location=AMap.Util.parseLngLatData(p.location);pp.id=p.id,location&&!Array.isArray(location)&&(pp.y=location.lat,pp.x=location.lng),pp.name=p.name,pp.address=p.address,AMap.plugin(\\\"AMap.CallAMap\\\",function(){var callapp=new AMap.CallAMap,url=callapp.getPoiUrl(pp);callapp.open(url)})}},{key:\\\"detailOnAMAP\\\",value:function(p){var pp={},location=AMap.Util.parseLngLatData(p.location);pp.id=p.id,location&&!Array.isArray(location)&&(pp.y=location.lat,pp.x=location.lng),pp.name=p.name,AMap.plugin(\\\"AMap.CallAMap\\\",function(){var callapp=new AMap.CallAMap,url=callapp.getPoiDetailUrl(pp);callapp.open(url)})}},{key:\\\"close\\\",value:function(){this.closed=!0}},{key:\\\"open\\\",value:function(){this.closed=!1}},{key:\\\"_mergeParams\\\",value:function(data,map,params){for(var i in data)void 0!==data[i]&&void 0!==map[i]&&params.push(map[i]+\\\"=\\\"+data[i]);params.push(\\\"language=\\\"+this.getLang())}},{key:\\\"_query\\\",value:function(url,params,cbk,encodeURICom,type){var _this2=this,args=5<arguments.length&&void 0!==arguments[5]?arguments[5]:{},params=AMap.extend(params,{type_:type,antiCrab:!0},encodeURICom);AMap.WebService.get(url,params,function(status,data){return\\\"complete\\\"===status?_this2._onComplete(data,cbk,type,args):\\\"error\\\"===status&&_this2._onError(data,cbk),{}})}},{key:\\\"_parseString\\\",value:function(v){return v instanceof Array&&0===v.length||void 0===v?\\\"\\\":v}},{key:\\\"_formatFields\\\",value:function(obj){for(var j in obj)obj.hasOwnProperty(j)&&(obj[j]=this._parseString(obj[j]),\\\"object\\\"===_typeof(obj[j])&&this._formatFields(obj[j]))}},{key:\\\"_str2LngLat\\\",value:function(str){var arr;return str&&(2===(arr=str.split(\\\",\\\")).length?new AMap.LngLat(parseFloat(arr[0]),parseFloat(arr[1])):str)}},{key:\\\"_parsePOI\\\",value:function(poi){function getLngLat(location){return location?(location=location.split(\\\",\\\"),new AMap.LngLat(parseFloat(location[0]),parseFloat(location[1]))):null}this._formatFields(poi);var poiBaseInfo={id:poi.id,name:poi.name,type:poi.type,location:getLngLat(poi.location),address:poi.address,tel:poi.tel,distance:parseFloat(poi.distance),shopinfo:poi.shopinfo};if(poi.shopinfo&&poi.shopinfo.length?poiBaseInfo.shopinfo=poi.shopinfo:poiBaseInfo.shopinfo=\\\"\\\",poi.children){for(var children=poi.children,i=0,len=children.length;i<len;i++)children[i].location=getLngLat(children[i].location);poiBaseInfo.children=children}var poiDetailInfo={website:poi.website,pcode:poi.pcode,citycode:poi.citycode,adcode:poi.adcode,postcode:poi.postcode,pname:poi.pname,cityname:poi.cityname,adname:poi.adname,email:poi.email,photos:poi.photos,entr_location:getLngLat(poi.entr_location),exit_location:getLngLat(poi.exit_location),groupbuy:\\\"0\\\"!==poi.groupbuy_num,discount:\\\"0\\\"!==poi.discount_num},poiDeepInfo=(\\\"1\\\"===poi.indoor_map?(poiDetailInfo.indoor_map=!0,poiDetailInfo.indoor_data={cpid:this._parseString(poi.indoor_data.cpid),floor:this._parseString(poi.indoor_data.floor),truefloor:this._parseString(poi.indoor_data.truefloor)}):poiDetailInfo.indoor_map=!1,{groupbuys:\\\"\\\",discounts:\\\"\\\",deep_type:\\\"\\\",dining:\\\"\\\",hotel:\\\"\\\",cinema:\\\"\\\",scenic:\\\"\\\"});if(poi.rich_content){if(poiDetailInfo.groupbuy){var groupBuyTemp=poi.rich_content.groupbuys,groupBuyNum=groupBuyTemp.length;0<groupBuyNum&&(poiDeepInfo.groupbuys=[]);for(var n=0;n<groupBuyNum;n+=1){var groupBuy={title:groupBuyTemp[n].title,type_code:groupBuyTemp[n].typecode,type:groupBuyTemp[n].type,detail:groupBuyTemp[n].detail,stime:groupBuyTemp[n].start_time,etime:groupBuyTemp[n].end_time,count:groupBuyTemp[n].num,sold_num:parseInt(groupBuyTemp[n].sold_num,10),original_price:parseFloat(groupBuyTemp[n].original_price),groupbuy_price:parseFloat(groupBuyTemp[n].groupbuy_price),discount:parseFloat(groupBuyTemp[n].discount),ticket_address:groupBuyTemp[n].ticket_address,ticket_tel:groupBuyTemp[n].ticket_tel,photos:groupBuyTemp[n].photos,url:groupBuyTemp[n].url,provider:\\\"\\\"};poiDeepInfo.groupbuys.push(groupBuy)}}if(poiDetailInfo.discount){var discountTemp=poi.rich_content.discounts,disNum=discountTemp.length;0<disNum&&(poiDeepInfo.discounts=[]);for(var dis=0;dis<disNum;dis+=1){var discount={title:discountTemp[dis].title,detail:discountTemp[dis].detail,start_time:discountTemp[dis].start_time,end_time:discountTemp[dis].end_time,sold_num:parseInt(discountTemp[dis].sold_num,10),photos:discountTemp[dis].photos,url:discountTemp[dis].url,provider:\\\"\\\"};poiDeepInfo.discounts.push(discount)}}}if(poi.deep_info){poiDeepInfo.deep_type=poi.deep_info.type;var poiDeep=poi.deep_info;switch(poiDeepInfo.deep_type){case\\\"cinema\\\":var cinema={intro:poiDeep.intro,rating:poiDeep.rating,deep_src:\\\"\\\",parking:poiDeep.parking,opentime_GDF:poiDeep.opentime_GDF,opentime:poiDeep.opentime,photos:poiDeep.photos};poiDeepInfo.cinema=cinema,poiDeepInfo.deep_type=\\\"CINEMA\\\";break;case\\\"dining\\\":cinema={cuisines:poiDeep.cuisines,tag:poiDeep.tag,intro:poiDeep.intro,rating:poiDeep.rating,cp_rating:poiDeep.cp_rating,deep_src:\\\"\\\",taste_rating:poiDeep.taste_rating,environment_rating:poiDeep.environment_rating,service_rating:poiDeep.service_rating,cost:poiDeep.cost,recommend:poiDeep.recommend,atmosphere:poiDeep.atmosphere,ordering_wap_url:poiDeep.ordering_wap_url,ordering_web_url:poiDeep.ordering_web_url,ordering_app_url:poiDeep.ordering_app_url,opentime_GDF:poiDeep.opentime_GDF,opentime:poiDeep.opentime,addition:poiDeep.addition,photos:poiDeep.photos};poiDeepInfo.dining=cinema,poiDeepInfo.deep_type=\\\"DINING\\\";break;case\\\"scenic\\\":cinema={intro:poiDeep.intro,rating:poiDeep.rating,deep_src:\\\"\\\",level:poiDeep.level,price:poiDeep.price,season:poiDeep.season,recommend:poiDeep.recommend,theme:poiDeep.theme,ordering_wap_url:poiDeep.ordering_wap_url,ordering_web_url:poiDeep.ordering_web_url,opentime_GDF:poiDeep.opentime_GDF,opentime:poiDeep.opentime,photos:poiDeep.photos};poiDeepInfo.scenic=cinema,poiDeepInfo.deep_type=\\\"SCENIC\\\";break;case\\\"hotel\\\":cinema={rating:poiDeep.rating,star:poiDeep.star,intro:poiDeep.intro,lowest_price:poiDeep.lowest_price,faci_rating:poiDeep.faci_rating,health_rating:poiDeep.health_rating,environment_rating:poiDeep.environment_rating,service_rating:poiDeep.service_rating,traffic:poiDeep.traffic,addition:poiDeep.addition,deep_src:\\\"\\\",photos:poiDeep.photos};poiDeepInfo.hotel=cinema,poiDeepInfo.deep_type=\\\"HOTEL\\\"}}var baseK,rePoi={};for(baseK in poiBaseInfo)poiBaseInfo.hasOwnProperty(baseK)&&(rePoi[baseK]=poiBaseInfo[baseK]);if(this.opt.extensions&&\\\"base\\\"!==this.opt.extensions){for(var deepK in poiDeepInfo)poiDeepInfo.hasOwnProperty(deepK)&&poiDeepInfo[deepK]&&(rePoi[deepK]=poiDeepInfo[deepK]);for(var detailK in poiDetailInfo)poiDetailInfo.hasOwnProperty(detailK)&&(rePoi[detailK]=poiDetailInfo[detailK]);if(poi.biz_ext)for(var key in poi.biz_ext)poi.biz_ext.hasOwnProperty(key)&&poi.biz_ext[key].length&&void 0===rePoi[key]&&(rePoi[key]=poi.biz_ext[key])}return rePoi}},{key:\\\"_onComplete\\\",value:function(data,cbk,type,args){var result;if(!this.closed)if(parseInt(data.status,10)){if((result={info:data.info,poiList:{}}).poiList={pois:[],count:parseInt(data.count,10),pageIndex:this.opt.pageIndex,pageSize:this.opt.pageSize},data.pois)for(var i=0;i<data.pois.length;i+=1){var tmp=data.pois[i];result.poiList.pois[i]=this._parsePOI(tmp)}if(data.suggestion&&data.suggestion.keywords.length&&(result.keywordList=data.suggestion.keywords,result.info=\\\"TIP_KEYWORDS\\\"),data.suggestion&&data.suggestion.cities.length){for(var cities=data.suggestion.cities,citiesnew=[],j=0;j<cities.length;j+=1){var pro,city=cities[j];for(pro in city)city.hasOwnProperty(pro)&&\\\"num\\\"===pro&&(city.count=city[pro],delete city[pro]);city.count=parseInt(city.count,10),citiesnew.push(city)}result.info=\\\"TIP_CITIES\\\",result.cityList=citiesnew}data.count&&0!==parseInt(data.count,10)||result.cityList||result.keywordList?(this.emit(\\\"complete\\\",result),(this.opt.map||this.opt.panel)&&this._autoRender(result,type,args),cbk&&\\\"function\\\"==typeof cbk&&cbk(\\\"complete\\\",result)):(result.info=\\\"ok\\\"===data.info.toLowerCase()?\\\"NO_DATA\\\":data.info,this.emit(\\\"complete\\\",result),(this.opt.map||this.opt.panel)&&this._autoRender(result,type,args),cbk&&\\\"function\\\"==typeof cbk&&(result.cityList||result.keywordList?cbk(\\\"complete\\\",result):cbk(\\\"no_data\\\",{})))}else result={info:data.info},this.emit(\\\"error\\\",result),cbk&&\\\"function\\\"==typeof cbk&&cbk(\\\"error\\\",data.info)}},{key:\\\"_autoRender\\\",value:function(result,type,args){this.render||(this.render=new this.renderEngine(this.opt.showCover));this.render.autoRender({data:result,eventTrigger:function(type,params){this.emit(type,params)},methodName:{KEYWORD:\\\"search\\\",NEARBY:\\\"searchNearBy\\\",POLYGON:\\\"searchInBounds\\\",ROUTE:\\\"searchNearRoute\\\"}[type],placeSearchInstance:this,methodArgumments:args,renderStyle:this.opt.renderStyle||\\\"newpc\\\",panel:this.opt.panel,map:this.opt.map,autoFitView:this.opt.autoFitView}),this._searchCircle=this.render.searchCircle}},{key:\\\"_onError\\\",value:function(e,cbk){this.emit(\\\"error\\\",e),cbk&&\\\"function\\\"==typeof cbk&&cbk(\\\"error\\\",e.info)}},{key:\\\"_filterData\\\",value:function(data){for(var key in data)data.hasOwnProperty(key)&&void 0===data[key]&&delete data[key]}}]),PlaceSearch}();AMap.PlaceSearch=PlaceSearch}(window.AMap); \",\"css\":\".amap_lib_placeSearch{font-family:Microsoft Yahei,Helvetica Neue,Helvetica,Arial,sans-serif;color:#565656;font-size:12px;line-height:22px;word-wrap:break-word;background-color:#fff;border:1px solid silver;-webkit-text-size-adjust:none;text-size-adjust:none;min-width:270px}.amap_lib_placeSearch .pageLink{cursor:pointer;line-height:16px;display:inline-block;text-align:center;padding:0 4px}.amap_lib_placeSearch.mobile .pageLink{border:1px solid #ddd;background:#eee;padding:2px 3px}.amap_lib_placeSearch .amap_lib_placeSearch_page .current{border-color:#0091ff;color:#0091ff;margin-left:-2px}.amap_lib_placeSearch_list{background:#fff none repeat scroll 0 0}.amap_lib_placeSearch .poibox .poibox-icon{margin-left:7px;margin-top:4px}.amap_lib_placeSearch_page{white-space:nowrap;text-align:right;margin:8px 0 5px;padding:2px;overflow:hidden}.amap_lib_placeSearch_page>div{float:left;margin-right:5px}.amap_lib_placeSearch_page>div>p{margin:0;padding:0;white-space:nowrap}.amap_lib_placeSearch_page>div>p>span{display:inline-block;padding:0 2px;min-width:15px;height:18px;border:1px solid #d9d9d9;border-radius:4px;line-height:18px;text-align:center;margin-right:3px}.amap_lib_placeSearch_page>div>p>span:hover{border-color:#1890ff;color:#1890ff;cursor:pointer}.amap_lib_placeSearch_ol{list-style:outside none none;padding:0;margin:0}.amap_lib_placeSearch_li{margin:2px 0;padding:0 5px 5px 0;cursor:pointer;overflow:hidden;line-height:17px}.amap_lib_placeSearch_li .amap_lib_placeSearch_li_wrap{overflow:hidden;padding:0 5px}.amap_lib_placeSearch_li_wrap_selected{background-color:#f0f0f0}.amap_lib_placeSearch_li>div .amap_lib_placeSearch_li_title{line-height:20px;font-size:12px}.amap_lib_placeSearch_li>div .amap_lib_placeSearch_li_title span{color:#00c}.amap_lib_placeSearch_li>div .amap_lib_placeSearch_li_title a{margin-left:5px;font-size:12px;color:#3d6dcc;font-weight:400;text-decoration:none}.amap_lib_placeSearch_li>div .amap_lib_placeSearch_li_text{padding:2px 0;line-height:18px;*zoom:1;overflow:hidden}.amap_lib_placeSearch_li>div .amap_lib_placeSearch_li_text b{float:left;font-weight:700;*zoom:1;overflow:hidden;padding-right:5px;*margin-right:-3px}.amap_lib_placeSearch_li>div .amap_lib_placeSearch_li_text span{color:#666;display:block;zoom:1;overflow:hidden}.amap-lib-infowindow .amap-lib-infowindow-content-wrap{word-break:break-all;overflow:hidden;zoom:1}.amap-lib-infowindow{padding:0;position:relative;background-color:#fff;margin:8px}.amap-lib-infowindow-title{line-height:22px;font-size:14px;border-bottom:1px solid #99adce;padding-right:15px}.amap-lib-infowindow-content{padding-top:5px;overflow:hidden;font-size:12px;zoom:1}.selected .amap_lib_placeSearch_poi{background-image:url(https://a.amap.com/jsapi/static/image/plugin/marker_red.png)}.amap_lib_placeSearch_poi{background:url(https://a.amap.com/jsapi/static/image/plugin/marker_red.png) no-repeat;background-size:20px 25px;width:20px;height:27px;cursor:pointer;left:-1px;text-align:center;color:#fff;font:12px arial,simsun,sans-serif;padding-top:3px}.amap_lib_placeSearch_pic{width:46px;height:46px;float:left;margin:4px 10px 0 0}.amap_lib_placeSearch_pic img{width:46px;height:46px}.selected .amap_lib_placeSearch_child_poi{background-image:url(https://a.amap.com/jsapi/static/image/plugin/marker_red.png)}.amap_lib_placeSearch_child_poi{width:11px;height:11px;border:1px solid #eee;border-radius:6px;background:url(https://a.amap.com/jsapi/static/image/plugin/marker_blue.png) no-repeat 50%;text-indent:-10000em;overflow:hidden}.poi-children-box{padding:3px 0 3px 25px}.poi-child-item.selected{border-color:#e17070}.poi-child-item{display:inline-block;line-height:180%;border:1px solid #ccc;background:#f3f3f3;margin:0 3px 3px 0;padding:0 3px;width:27%;min-width:40px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;vertical-align:middle;text-align:center}.poi-child-item:hover{background:#ddd}.amap_lib_placeSearch{border:1px solid #ebedf0;border-radius:2px;padding:5px 10px 2px}.amap_lib_placeSearch .amap-ellipsis{max-height:20px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.amap_lib_placeSearch .amap_lib_placeSearch_poi{position:absolute}.amap_lib_placeSearch ol,.amap_lib_placeSearch ul{list-style:outside none none;margin:0;padding:0}.amap_lib_placeSearch .poibox{border-bottom:1px solid #e8e8e8;cursor:pointer;padding:10px 5px;position:relative;min-height:35px}.amap_lib_placeSearch .poibox:last-child{border-bottom:none}.amap_lib_placeSearch .poibox .poi-title{margin-left:25px;font-size:14px;font-weight:700;overflow:hidden}.amap_lib_placeSearch .poibox .poi-title a{font-weight:400;text-decoration:none;color:#666;position:absolute;right:2px}.amap_lib_placeSearch .poibox .poi-title a:hover{color:#0091ff}.amap_lib_placeSearch .poibox .poi-title .poi-name{max-width:160px;display:inline-block}.amap_lib_placeSearch .poi-more{color:#0091ff;font-size:12px;line-height:22px;white-space:nowrap;vertical-align:baseline}.amap_lib_placeSearch .poibox .poi-info{word-break:break-all;margin:0 0 0 25px;overflow:hidden}.amap_lib_placeSearch .poibox .poi-info p{color:#999;font-family:Tahoma;line-height:20px}.amap_lib_placeSearch p{margin:0}.amap_lib_placeSearch h1,.amap_lib_placeSearch h2,.amap_lib_placeSearch h3,.amap_lib_placeSearch h4,.amap_lib_placeSearch h5{font-weight:400;margin:0}.amap_lib_placeSearch .poibox.active,.amap_lib_placeSearch .poibox.hover,.amap_lib_placeSearch .poibox:hover{background:#fafafa none repeat scroll 0 0}.amap_lib_placeSearch .poibox .select-btn{margin-left:25px;margin-top:6px;border:0;color:#fff;cursor:pointer;padding:3px 6px;border-radius:2px}.amap-combo-close{position:absolute;top:11px;right:10px;background:url(http://webapi.amap.com/theme/v1.3/images/amap-info.png) no-repeat -1px -151px;width:12px;height:12px;cursor:pointer}.amap-content-body{min-width:200px;max-width:240px;font-family:Helvetica,Hiragino Sans GB,Microsoft Yahei,微软雅黑,Arial,sans-serif;box-shadow:0 0 .5px rgba(0,0,100,.6);background:none repeat scroll 0 0 #fff;border-radius:2px;text-align:left;border:1px solid silver}.amap-combo-sharp{margin:0 auto;bottom:1px;position:relative;background:url(http://webapi.amap.com/theme/v1.3/images/amap-info.png) no-repeat -5px -564px;width:18px;height:9px}.amap-pl-pc .poi-img{float:right;margin:3px 8px 0;width:90px;height:56px;overflow:hidden}.amap-pl-pc .poi-name{vertical-align:middle}.amap-pl-pc .poi-more{display:inline-block;width:16px;height:16px;text-indent:-1000em;background:url(http://webapi.amap.com/theme/v1.3/images/newpc/tips.png) no-repeat 50%;vertical-align:middle;cursor:pointer;opacity:.5;margin:1px 0 1px 2px;background-size:85%}.amap-pl-pc .poi-more:hover{opacity:1;background-size:100%}.amap_lib_placeSearch .clear{clear:both}.amap-pls-marker-tip{position:absolute;background:#fff;display:none;top:0;left:-100px;min-width:100px;width:auto;white-space:nowrap;line-height:200%;padding:0 0 0 7px;font-size:13px;border-radius:2px;-webkit-box-shadow:0 0 8px 0 rgba(0,0,0,.5);-moz-box-shadow:0 0 8px 0 rgba(0,0,0,.5);box-shadow:0 0 8px 0 rgba(0,0,0,.5);transition:all .5s 1s;z-index:150}.amap-pls-marker-tip .title{display:inline-block;vertical-align:middle;max-width:150px;overflow:hidden;text-overflow:ellipsis;margin-right:35px}.amap_lib_placeSearch_child_con .amap-pls-marker-tip{top:-7px}.amap-marker .hover .amap_lib_placeSearch_child_con .amap-pls-marker-tip{left:23px}.amap-marker .hover .amap-pls-marker-tip{display:block;left:28px}.amap-marker .hover.selected .amap-pls-marker-tip{display:none}.amap-pls-marker-tip:after,.amap-pls-marker-tip:before{content:\\\"\\\";width:0;height:0;top:50%;left:0;margin-top:-7px;margin-left:-15px;position:absolute;border:7px solid transparent;border-right:8px solid rgba(51,51,51,.2)}.amap-pls-marker-tip:after{margin-left:-14px;border-right:8px solid #fff}\"}"}, {"name": "_AMap_AMap.Scale", "value": "{\"version\":\"1736748443937\",\"script\":\"(function(){\\\"use strict\\\";var extendStatics=function(d,b){extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(d,b){d.__proto__=b}||function(d,b){for(var p in b)if(Object.prototype.hasOwnProperty.call(b,p))d[p]=b[p]};return extendStatics(d,b)};function __extends(d,b){if(typeof b!==\\\"function\\\"&&b!==null)throw new TypeError(\\\"Class extends value \\\"+String(b)+\\\" is not a constructor or null\\\");extendStatics(d,b);function __(){this.constructor=d}d.prototype=b===null?Object.create(b):(__.prototype=b.prototype,new __)}var _SuppressedError=typeof SuppressedError===\\\"function\\\"?SuppressedError:function(error,suppressed,message){var e=new Error(message);return e.name=\\\"SuppressedError\\\",e.error=error,e.suppressed=suppressed,e};var defultOpts={position:\\\"LB\\\",offset:[15,25]};var Scale=function(_super){__extends(Scale,_super);function Scale(opts){if(opts===void 0){opts=defultOpts}var _this=_super.call(this,opts)||this;_this._className=\\\"scalecontrol\\\";_this._scaleLevel=[[1e7,\\\"10000\\\"],[5e6,\\\"5000\\\"],[2e6,\\\"2000\\\"],[1e6,\\\"1000\\\"],[5e5,\\\"500\\\"],[2e5,\\\"200\\\"],[1e5,\\\"100\\\"],[5e4,\\\"50\\\"],[3e4,\\\"30\\\"],[2e4,\\\"20\\\"],[1e4,\\\"10\\\"],[5e3,\\\"5\\\"],[2e3,\\\"2\\\"],[1e3,\\\"1\\\"],[500,\\\"500\\\"],[200,\\\"200\\\"],[100,\\\"100\\\"],[50,\\\"50\\\"],[25,\\\"25\\\"],[10,\\\"10\\\"],[5,\\\"5\\\"],[2,\\\"2\\\"],[1,\\\"1\\\"],[.5,\\\"50\\\"],[.2,\\\"20\\\"],[.1,\\\"10\\\"],[.05,\\\"5\\\"],[.02,\\\"2\\\"],[.01,\\\"1\\\"],[.01,\\\"1\\\"],[.01,\\\"1\\\"]];_this._handleViewChange=_this._handleViewChange.bind(_this);_this._config=AMap.extend(defultOpts,opts);_this.initContainer();return _this}Scale.prototype.addTo=function(map){_super.prototype.addTo.call(this,map);this._initUI();map.on(\\\"viewchange\\\",this._handleViewChange)};Scale.prototype.remove=function(){if(this.map){this.map.off(\\\"viewchange\\\",this._handleViewChange)}this._destroyUI();_super.prototype.remove.call(this)};Scale.prototype.removeFrom=function(map){if(map){map.off(\\\"viewchange\\\",this._handleViewChange)}this._destroyUI();_super.prototype.removeFrom.call(this,map)};Scale.prototype[\\\"getScaleText\\\"]=function(){if(this._text){return this._text.innerText}};Scale.prototype[\\\"getScaleLength\\\"]=function(){if(this._lineMid&&this._lineMid.style){return this._lineMid.style.width}};Scale.prototype._handleViewChange=function(){this._updateUI()};Scale.prototype._initUI=function(){this._text=AMap.DomUtil.create(\\\"div\\\",this._container,\\\"amap-scale-text\\\");this._line=AMap.DomUtil.create(\\\"div\\\",this._container,\\\"amap-scale-line\\\");this._lineLeft=AMap.DomUtil.create(\\\"div\\\",this._container,\\\"amap-scale-edgeleft\\\");this._lineRight=AMap.DomUtil.create(\\\"div\\\",this._container,\\\"amap-scale-edgeright\\\");this._lineMid=AMap.DomUtil.create(\\\"div\\\",this._container,\\\"amap-scale-middle\\\");this._line.appendChild(this._lineLeft);this._line.appendChild(this._lineMid);this._line.appendChild(this._lineRight);this._updateUI()};Scale.prototype._destroyUI=function(){while(this._container.firstChild){this._container.removeChild(this._container.firstChild)}};Scale.prototype._updateUI=function(){var _a=this.map.getView().getStatus(),optimalZoom=_a.optimalZoom,center=_a.center,resolution=_a.resolution;var size=this.map.getSize().toArray();var level=optimalZoom-1;var coordX=resolution*Math.cos(center[1]/180*Math.PI);var len=this._scaleLevel[level][0]/coordX;for(var i=level;i<this._scaleLevel.length;i++){len=this._scaleLevel[level][0]/coordX;if(len<120){break}level++;level=Math.min(level,this._scaleLevel.length-1)}var scale=this._scaleLevel[level][1];if(level<=13){scale+=\\\" 公里\\\"}else if(level<23){scale+=\\\" 米\\\"}else{scale+=\\\" 厘米\\\"}this._text.innerText=scale;this._text.style.width=len+8+\\\"px\\\";this._lineMid.style.width=len+\\\"px\\\";this._lineRight.style.left=len+1+\\\"px\\\"};return Scale}(AMap.Control);AMap[\\\"Scale\\\"]=Scale})(); \"}"}]}]}