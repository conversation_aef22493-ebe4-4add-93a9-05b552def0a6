// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption, safeAIQuery } from "../../common/test-helpers";
import * as fs from 'fs';
import * as path from 'path';
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'renmai';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站人脉广场SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('人脉广场-频道页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.connectionChannelPage;

    await page.goto(URL_CONFIG.connection);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;
    (testInfo as any).currentPageForReport = page;

    console.log(`人脉广场频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `人脉广场频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('人脉广场-列表页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.connectionListPage;

    await page.goto(`${URL_CONFIG.connection}/list`);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;
    (testInfo as any).currentPageForReport = page;

    console.log(`人脉广场列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `人脉广场列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('人脉广场-详情页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试人脉广场详情页');

    await page.goto(`${URL_CONFIG.connection}/list`);
    await page.waitForLoadState('networkidle');
    await page.waitForSelector('.content > a', { timeout: 10000 });

    const personCard = page.locator('.content > a').first();
    const personName = await personCard.locator('[class*="text-[18px]"]').textContent() || '示例人脉';
    const companyName = await personCard.locator('div[class*="text-[14px]"]').first().textContent() || '示例企业';

    const [newPage] = await Promise.all([
      page.context().waitForEvent('page'),
      personCard.locator('.duijie', { hasText: '我要对接' }).click()
    ]);
    await newPage.waitForLoadState('networkidle');
    
    // 等待3秒钟，确保页面内容完全加载和渲染
    console.log('等待3秒钟，确保页面完全加载...');
    await newPage.waitForTimeout(3000);
    console.log('新tab已打开并加载完成。');
    (testInfo as any).currentPageForReport = newPage;

    const currentUrl = newPage.url();
    const personId = currentUrl.split('/').pop()?.split('?')[0] || '000E7MG00YQFSFAGF';

    const detailTestData = SEOTestData.connectionDetailPage(
      companyName,
      personName,
      personId
    );
    
    const seoValidatorForNewPage = new SEOValidatorPage(newPage);
    const result = await seoValidatorForNewPage.validateTDKWithSoftAssertions({
      title: detailTestData.title,
      keywords: Array.isArray(detailTestData.keywords) ? detailTestData.keywords : [detailTestData.keywords].filter(Boolean),
      description: detailTestData.description,
      url: detailTestData.url
    });
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    console.log(`人脉广场详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${Array.isArray(detailTestData.keywords) ? detailTestData.keywords.join(', ') : detailTestData.keywords}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);
    expect(result.success, `人脉广场详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();

    try {
      if (newPage && !newPage.isClosed()) {
        await newPage.close();
        console.log('人脉广场详情页 (newPage) 已关闭 (renmai.spec.ts)');
      }
    } catch (e) {
      console.error('关闭 newPage 时发生错误 (renmai.spec.ts):', e);
    }
  });
}); 