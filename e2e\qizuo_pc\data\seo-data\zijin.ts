/**
 * 资金服务SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class ZijinSEOData extends BaseSEOData {
  // 资金服务-频道页
  static fundingServiceChannelPage: SEOTestDataItem = {
    title: "找投资机构 寻上市公司 享奖补政策 金融科技服务 - 企座",
    keywords: [
      "找投资机构",
      "寻上市公司"
    ],
    description: "企座金融服务聚合红杉/IDG等超900家投资方及超过2000家有并购需求的上市公司，为创新企业提供多种获取资金渠道。",
    url: "zijinfuwu.aitojoy.com"
  };

  // 资金服务-机构列表页
  static fundingServiceListPage: SEOTestDataItem = {
    title: "按所在地寻找投资机构 按行业寻找投资机构 - 企座",
    keywords: [
      "按所在地寻找投资机构",
      "按行业寻找投资机构"
    ],
    description: "企座投资机构列表页，支持中小创新企业根据您的所在地及行业，快速寻找匹配您业务发展的投资机构。平台提供投资机构对接服务，留下联系方式及需求即可对接。",
    url: "zijinfuwu.aitojoy.com/list"
  };

  /**
   * 资金服务-详情页
   * @param institutionName 机构名称
   * @param institutionId 机构ID
   * @param institutionIntro 机构简介
   * @returns SEO数据对象
   */
  static fundingServiceDetailPage = (
    institutionName: string,
    institutionId: string,
    institutionIntro: string = ''
  ) => {
    return {
      title: `${institutionName}的偏好行业、机构成员、投资事件、管理基金等信息 - 企座`,
      keywords: [
        `了解${institutionName}投资偏好行业`,
        `了解${institutionName}机构成员`,
        `了解${institutionName}投资事件`,
        `了解${institutionName}管理基金`,
        `了解${institutionName}新闻舆情`
      ],
      description: institutionIntro || `${institutionName}的详细介绍`,
      url: `zijinfuwu.aitojoy.com/detail/${institutionId}`
    };
  };
} 