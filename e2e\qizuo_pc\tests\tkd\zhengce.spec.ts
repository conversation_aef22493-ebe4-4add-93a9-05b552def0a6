// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption, safeAIQuery } from "../../common/test-helpers";
import * as fs from 'fs';
import * as path from 'path';
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'zhengce';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站惠企政策SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('惠企政策-频道页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.policyChannelPage;

    await page.goto(URL_CONFIG.policy);
    await page.waitForLoadState('networkidle');

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log('验证的TDK信息:');
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    expect(result.success, `惠企政策频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('惠企政策-列表页-奖补政策tabSEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.policyRewardListPage;

    await page.goto(`${URL_CONFIG.policy}/jiangbu`);
    await page.waitForLoadState('networkidle');

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`惠企政策-列表页-奖补政策tabSEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    expect(result.success, `惠企政策-列表页-奖补政策tabSEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('惠企政策-列表页-热点政策tabSEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.policyHotListPage;

    await page.goto(`${URL_CONFIG.policy}/redian`);
    await page.waitForLoadState('networkidle');

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`惠企政策-列表页-热点政策tabSEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    expect(result.success, `惠企政策-列表页-热点政策tabSEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('惠企政策-详情页-奖补政策SEO元素验证', async ({ page, aiQuery, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试惠企政策-详情页-奖补政策');

    await page.goto(`${URL_CONFIG.policy}/jiangbu`);
    await page.waitForLoadState('networkidle');

   
    const waitForNewPage = page.context().waitForEvent('page').catch(() => null);

    try {
      await aiTap('第一个政策的标题');
      console.log('aiTap执行完成');
    } catch (e) {
      console.log('aiTap执行异常:', e);
      await page.locator('.policy-item').first().click();
    }
    await page.waitForTimeout(3000);
    let targetPage = await Promise.race([
      waitForNewPage,
      page.waitForURL('**', { waitUntil: 'networkidle' }).then(() => null)
    ]);

    if (!targetPage || (targetPage && targetPage.isClosed())) {
      targetPage = page;
      console.log('未打开新tab，继续在当前页');
    } else {
      console.log('新tab已获取:', !!targetPage);
      await targetPage.waitForLoadState('networkidle');
    }
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    
    const currentUrl = targetPage.url();
    const policyId = currentUrl.split('/').pop() || 'policy123';
    const policyName = await aiQuery('政策的标题') || '示例政策';
    const detailTestData = SEOTestData.policyRewardDetailPage(
      policyName,
      policyId
    );

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: detailTestData.title,
      keywords: Array.isArray(detailTestData.keywords) ? detailTestData.keywords : [detailTestData.keywords].filter(Boolean),
      description: detailTestData.description,
      url: detailTestData.url
    }, targetPage);
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    console.log('验证的TDK信息:');
    console.log(`关键词: ${Array.isArray(detailTestData.keywords) ? detailTestData.keywords.join(', ') : detailTestData.keywords}`);
    expect(result.success, `惠企政策-详情页-奖补政策SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();

    if (targetPage !== page && !targetPage.isClosed()) {
      await targetPage.close();
    }
  });

  test('惠企政策-详情页-热点政策SEO元素验证', async ({ page, aiQuery, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试惠企政策-详情页-热点政策');

    await page.goto(`${URL_CONFIG.policy}/redian`);
    await page.waitForLoadState('networkidle');

    const waitForNewPage = page.context().waitForEvent('page').catch(() => null);

    try {
      await aiTap('第一个政策的标题');
      console.log('aiTap执行完成');
    } catch (e) {
      console.log('aiTap执行异常:', e);
      await page.locator('.policy-item').first().click();
    }

    let targetPage = await Promise.race([
      waitForNewPage,
      page.waitForURL('**', { waitUntil: 'networkidle' }).then(() => null)
    ]);

    if (!targetPage || (targetPage && targetPage.isClosed())) {
      targetPage = page;
      console.log('未打开新tab，继续在当前页');
    } else {
      console.log('新tab已获取:', !!targetPage);
      await targetPage.waitForLoadState('networkidle');
    }

    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const currentUrl = targetPage.url();
    const policyId = currentUrl.split('/').pop() || 'policy123';
    const policyName = await safeAIQuery(aiQuery, '政策的标题', '示例政策');

    const detailTestData = SEOTestData.policyHotDetailPage(
      policyName,
      policyId
    );

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: detailTestData.title,
      keywords: Array.isArray(detailTestData.keywords) ? detailTestData.keywords : [detailTestData.keywords].filter(Boolean),
      description: detailTestData.description,
      url: detailTestData.url
    }, targetPage);
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    console.log('验证的TDK信息:');
    console.log(`关键词: ${Array.isArray(detailTestData.keywords) ? detailTestData.keywords.join(', ') : detailTestData.keywords}`);
    expect(result.success, `惠企政策-详情页-热点政策SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();

    if (targetPage !== page && !targetPage.isClosed()) {
      await targetPage.close();
    }
  });
}); 