// @ts-check
import { test } from "./fixture";
import { expect } from "@playwright/test";
import { MeetingSystemPage } from "./pages/meeting-system.page";

test.beforeEach(async ({ page }) => {
  await page.goto(process.env.HOUTAI_URL!);
});

test("houtai会议系统后台", async ({ page, ai, aiTap, aiAssert, aiWaitFor }) => {
  // 创建会议系统页面对象，传入AI驱动功能
  const meetingSystemPage = new MeetingSystemPage(page, { ai, aiTap, aiAssert, aiWaitFor } as any);

  // 验证用户按钮是否可见
  await meetingSystemPage.verifyUserButtonVisible();

  // 导航到会议系统后台
  await meetingSystemPage.navigateToMeetingSystem();

  // 导航到各个功能模块
  await meetingSystemPage.navigateToMeetingActivity();
  await meetingSystemPage.navigateToResourceManagement();
  await meetingSystemPage.navigateToOrderCenter();
  await meetingSystemPage.navigateToPromotionManagement();
  await meetingSystemPage.navigateToMeetingActivity();
  await meetingSystemPage.navigateToSettingCenter();
});
