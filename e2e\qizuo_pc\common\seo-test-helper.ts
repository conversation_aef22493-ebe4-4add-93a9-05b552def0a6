/**
 * SEO测试辅助函数，提供统一的测试钩子和测试结果处理逻辑
 */
import { Page } from '@playwright/test';
import { TestInfo } from '@playwright/test';
import { SEOReportItem } from '../common/base-config';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 处理SEO测试的afterEach钩子
 * @param page Playwright页面对象
 * @param testInfo 测试信息对象
 * @param currentFileReportItems 当前文件的测试结果集合
 * @param source 测试源文件名，用于日志和错误提示
 */
export async function handleSEOTestAfterEach(
  page: Page | null, 
  testInfo: TestInfo, 
  currentFileReportItems: SEOReportItem[],
  source: string = 'seo-test'
): Promise<void> {
  // 处理普通测试数据
  const testData = (testInfo as any).seoTestData;
  const validationResult = (testInfo as any).seoValidationResult;
  
  if (testData && validationResult) {
    // 如果是基本测试数据，添加到报告中（如果尚未添加过）
    const hasReportItem = currentFileReportItems.some(item => 
      item.testCaseTitle === testInfo.title
    );
    
    if (!hasReportItem) {
      const reportItem: SEOReportItem = {
        testCaseTitle: testInfo.title,
        status: validationResult.success ? 'Pass' : 'Fail',
        expectedTitle: testData.title,
        actualTitle: validationResult.actualElements.title,
        expectedKeywords: Array.isArray(testData.keywords) ? testData.keywords.join(', ') : String(testData.keywords || ''),
        actualKeywords: validationResult.actualElements.keywords ? validationResult.actualElements.keywords.split(/[\s,]+/).map(k => k.trim()).filter(k => k).join(', ') : '',
        expectedDescription: testData.description,
        actualDescription: validationResult.actualElements.description,
        url: validationResult.actualElements.url,
        expectedUrl: testData.url,
        errors: validationResult.errors.join('; ')
      };
      currentFileReportItems.push(reportItem);
    }
  } else {
    // 处理由testSEOWithFilter生成的测试数据
    const testInfoKeys = Object.keys(testInfo as any);
    const hasSeoTestData = testInfoKeys.some(key => 
      key.startsWith('seoTestData_') && testInfoKeys.some(rKey => rKey.startsWith('seoValidationResult_'))
    );
    
    if (hasSeoTestData) {
      // 记录发现筛选测试数据
      console.log(`[INFO] 发现testSEOWithFilter生成的测试数据，正在处理: ${testInfo.title}`);
      
      // 获取所有带前缀的测试数据和结果键
      const testDataKeys = testInfoKeys.filter(key => key.startsWith('seoTestData_'));
      const resultKeys = testInfoKeys.filter(key => key.startsWith('seoValidationResult_'));
      
      // 处理每对测试数据和结果
      for (const testDataKey of testDataKeys) {
        const suffix = testDataKey.replace('seoTestData_', '');
        const resultKey = `seoValidationResult_${suffix}`;
        
        if (resultKeys.includes(resultKey)) {
          const filterTestData = (testInfo as any)[testDataKey];
          const filterValidationResult = (testInfo as any)[resultKey];
          
          if (filterTestData && filterValidationResult) {
            // 构建此过滤条件测试的标题
            const filterTitle = `${testInfo.title} - ${suffix}`;
            
            // 检查是否已添加此测试结果
            const hasFilterReportItem = currentFileReportItems.some(item => 
              item.testCaseTitle === filterTitle
            );
            
            if (!hasFilterReportItem) {
              const reportItem: SEOReportItem = {
                testCaseTitle: filterTitle,
                status: filterValidationResult.success ? 'Pass' : 'Fail',
                expectedTitle: filterTestData.title,
                actualTitle: filterValidationResult.actualElements.title,
                expectedKeywords: Array.isArray(filterTestData.keywords) ? filterTestData.keywords.join(', ') : String(filterTestData.keywords || ''),
                actualKeywords: filterValidationResult.actualElements.keywords ? filterValidationResult.actualElements.keywords.split(/[\s,]+/).map(k => k.trim()).filter(k => k).join(', ') : '',
                expectedDescription: filterTestData.description,
                actualDescription: filterValidationResult.actualElements.description,
                url: filterValidationResult.actualElements.url,
                expectedUrl: filterTestData.url,
                errors: filterValidationResult.errors.join('; ')
              };
              currentFileReportItems.push(reportItem);
              console.log(`[INFO] 成功添加过滤测试结果: ${filterTitle}`);
            }
          }
        }
      }
    } else if (!currentFileReportItems.some(item => item.testCaseTitle.startsWith(testInfo.title)) && !testInfoKeys.some(key => key.startsWith('seoTestData_'))) {
      // 如果没有找到任何相关测试数据，并且它不是一个筛选测试的容器，则记录警告并添加错误项
      console.warn(`[WARN] SEOReportItem data not found for test: ${testInfo.title} in ${source}. This might be a normal test without SEO data or an issue.`);
      
      const errorReportItem: SEOReportItem = {
        testCaseTitle: testInfo.title,
        status: 'Fail',
        expectedTitle: `N/A (${source})`,
        actualTitle: 'N/A',
        expectedKeywords: 'N/A',
        actualKeywords: 'N/A',
        expectedDescription: 'N/A',
        actualDescription: 'N/A',
        url: page?.url() || 'N/A', 
        expectedUrl: 'N/A',
        errors: `Critical error: seoTestData or seoValidationResult not attached to testInfo in ${source}.`
      };
      currentFileReportItems.push(errorReportItem);
    }
  }

  // 关闭浏览器页面
  try {
    if (page && !page.isClosed()) {
      await page.close();
      console.log(`浏览器页面已关闭 (${source})`);
    }
  } catch (e) {
    console.error(`关闭浏览器页面时发生错误 (${source}):`, e);
  }
}

/**
 * 处理SEO测试的afterAll钩子
 * @param testInfo 测试信息对象
 * @param currentFileReportItems 当前文件的测试结果集合
 * @param source 测试源文件名，用于日志和错误提示
 */
export async function handleSEOTestAfterAll(
  testInfo: TestInfo, 
  currentFileReportItems: SEOReportItem[],
  source: string = 'seo-test'
): Promise<void> {
  // 输出调试信息，显示收集到的测试项
  console.log(`[DEBUG] ${source} 报告项内容 (总项数: ${currentFileReportItems.length}):`);
  currentFileReportItems.forEach((item, index) => {
    console.log(`[DEBUG] 项 #${index + 1}: ${item.testCaseTitle} - ${item.status} - ${item.url}`);
  });
  
  if (currentFileReportItems.length > 0) {
    // 确保临时数据目录存在
    const tmpJsonDir = path.join(process.cwd(), 'reports', 'tmp_seo_data');
    if (!fs.existsSync(tmpJsonDir)) {
      fs.mkdirSync(tmpJsonDir, { recursive: true });
    }
    
    // 生成安全的文件名
    const safeFileName = path.basename(testInfo.file, path.extname(testInfo.file)).replace(/[^a-zA-Z0-9_\-]/g, '_');
    
    // 为了避免worker之间的文件覆盖，使用更加区分性的文件名
    const timestamp = new Date().getTime();
    const testNames = [...new Set(currentFileReportItems.map(item => item.testCaseTitle.split(' - ')[0]))].join('_');
    const shortTestNames = testNames.length > 30 ? testNames.substring(0, 30) + '...' : testNames;
    const fileName = `${safeFileName}_worker${testInfo.parallelIndex}_${timestamp}_${shortTestNames.replace(/[^a-zA-Z0-9_\-]/g, '_')}.json`;
    
    const filePath = path.join(tmpJsonDir, fileName);

    // 写入临时JSON文件
    try {
      fs.writeFileSync(filePath, JSON.stringify(currentFileReportItems, null, 2), 'utf-8');
      console.log(`[SEO Test File Teardown] ${source} 临时SEO数据已保存到: ${filePath}`);
    } catch (err) {
      console.error(`[SEO Test File Teardown] ${source} 临时SEO数据写入失败 ${filePath}:`, err);
    }
  } else {
    console.log(`[SEO Test File Teardown] ${source} 未收集到测试结果，不生成临时文件`);
  }
} 