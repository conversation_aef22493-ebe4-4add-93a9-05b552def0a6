import { test as base } from "@playwright/test";
import type { PlayWrightAiFixtureType } from "@midscene/web/playwright";
import { PlaywrightAiFixture } from "@midscene/web/playwright";
import { envConfig } from "./config/environment.config";
import path from "path";

/**
 * 根据环境获取认证文件路径
 * @param type 认证类型 ('pc' | 'admin')
 * @returns 认证文件路径
 */
function getAuthFilePath(type: string): string {
  const env = envConfig.getEnvironment();
  
  // 打印环境变量调试信息
  console.log('\n=== 环境变量调试信息(fixture) ===');
  console.log(`process.env.QIZUO_ENV = "${process.env.QIZUO_ENV}"`);
  console.log(`process.env.NODE_ENV = "${process.env.NODE_ENV}"`);
  console.log(`envConfig.getEnvironment() = "${env}"`);
  console.log('===================================\n');
  
  return path.join(process.cwd(), `data/qizuo-${type}-auth-${env}.json`);
}

// 扩展测试固件，使用默认浏览器配置
export const test = base.extend<PlayWrightAiFixtureType & {
}>({
  ...PlaywrightAiFixture({
    forceSameTabNavigation: false // 允许导航到新标签页
  }),
  // 使用默认浏览器配置
  context: async ({ browser }, use) => {
    const context = await browser.newContext({
      // viewport: { width: 800, height: 600 },
      storageState: getAuthFilePath('pc')
    });
    await use(context);
  },
});