import path from 'path';
import fs from 'fs';

// 导入新的环境配置系统
import { envConfig, URL_CONFIG as NEW_URL_CONFIG } from '../config/environment.config';

export interface SEOReportItem {
  testCaseTitle: string;
  status: 'Pass' | 'Fail';
  expectedTitle: string;
  actualTitle: string | null;
  expectedKeywords: string; // 将数组转换为逗号分隔的字符串
  actualKeywords: string;   //  实际关键词也处理为逗号分隔的字符串
  expectedDescription: string;
  actualDescription: string | null;
  errors: string; // 将数组转换为分号分隔的字符串
  url: string; // 实际URL
  expectedUrl: string;
}

// 输出当前环境信息
console.log(`🚀 使用新的环境配置系统`);
envConfig.printConfig();

// 为了向后兼容，保留 BASE_URL 导出
export const BASE_URL = NEW_URL_CONFIG.main;

// 导出新的URL配置
export const URL_CONFIG = NEW_URL_CONFIG;

// 创建报告目录相关配置
export const REPORT_CONFIG = {
  reportDir: path.join(process.cwd(), 'reports', 'seo-reports'),
  ensureReportDirExists: () => {
    if (!fs.existsSync(REPORT_CONFIG.reportDir)) {
      fs.mkdirSync(REPORT_CONFIG.reportDir, { recursive: true });
    }
  },
  saveReport: (testTitle: string, report: string) => {
    REPORT_CONFIG.ensureReportDirExists();
    const safeTitle = testTitle.replace(/[\\/:*?"<>|\s]/g, '-');
    const reportPath = path.join(REPORT_CONFIG.reportDir, `${safeTitle}.txt`);
    fs.writeFileSync(reportPath, report);
    console.log(`SEO报告已保存至: ${reportPath}`);
  }
}; 