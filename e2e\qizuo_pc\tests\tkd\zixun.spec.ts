// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption, safeAIQuery } from "../../common/test-helpers";
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";
import * as fs from 'fs';
import * as path from 'path';

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'zixun';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站产业资讯SEO验证', () => {
  // 在每个测试后保存SEO报告
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 产业资讯-频道页测试
  test('产业资讯-频道页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNewsChannelPage;

    // 访问产业资讯频道页
    await page.goto(URL_CONFIG.news);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    // 记录验证结果
    console.log(`产业资讯频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯频道页SEO验证结果: ${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-列表页-7*24测试
  test('产业资讯-列表页-7*24SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNews24HListPage;

    // 访问产业资讯7*24列表页
    await page.goto(`${URL_CONFIG.news}/kuaixun`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    // 记录验证结果
    console.log(`产业资讯-列表页-7*24SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯-列表页-7*24SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-列表页-市场聚焦测试
  test('产业资讯-列表页-市场聚焦SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNewsFocusListPage;

    // 访问产业资讯市场聚焦列表页
    await page.goto(`${URL_CONFIG.news}/jujiao`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    // 记录验证结果
    console.log(`产业资讯-列表页-市场聚焦SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯-列表页-市场聚焦SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-列表页-市场洞察测试
  test('产业资讯-列表页-市场洞察SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNewsInsightListPage;

    // 访问产业资讯市场洞察列表页
    await page.goto(`${URL_CONFIG.news}/dongcha`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    // 记录验证结果
    console.log(`产业资讯-列表页-市场洞察SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯-列表页-市场洞察SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-列表页-商讯快报测试
  test('产业资讯-列表页-商讯快报SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryNewsBusinessListPage;

    // 访问产业资讯商讯快报列表页
    await page.goto(`${URL_CONFIG.news}/shangxunkuaibao`);

    // 等待页面加载完成
    await page.waitForLoadState('networkidle');

    // 滚动页面以确保所有内容加载
    await page.evaluate(() => window.scrollBy(0, 300));

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    // 记录验证结果
    console.log(`产业资讯-列表页-商讯快报SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯-列表页-商讯快报SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 产业资讯-详情页测试
  test('产业资讯-详情页SEO元素验证', async ({ page, aiQuery }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试产业资讯详情页');

    // 访问产业资讯详情页
    await page.goto(`${URL_CONFIG.news}/detail/68380806df26f46294132524?type=1`);
    await page.waitForLoadState('networkidle');

    // 等待3秒钟，确保页面内容完全加载和渲染
    console.log('等待3秒钟，确保页面完全加载...');
    await page.waitForTimeout(3000);
    console.log('页面已加载完成。');
    (testInfo as any).currentPageForReport = page;

    // 获取资讯ID
    const currentUrl = page.url();
    const newsId = currentUrl.split('/').pop()?.split('?')[0] || 'news123';
    
    // 获取资讯信息
    const newsTitle = await safeAIQuery(aiQuery, '资讯的标题', '示例资讯标题');
    const channelName = '资讯推荐';

    // 生成测试数据
    const detailTestData = SEOTestData.industryNewsDetailPage(
      channelName,
      newsTitle,
      newsId
    );

    // 使用软断言验证所有SEO元素
    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: detailTestData.title,
      keywords: Array.isArray(detailTestData.keywords) ? detailTestData.keywords : [detailTestData.keywords].filter(Boolean),
      description: detailTestData.description,
      url: detailTestData.url
    });
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    // 记录验证结果
    console.log(`产业资讯详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    // 详细输出验证的TDK信息
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${Array.isArray(detailTestData.keywords) ? detailTestData.keywords.join(', ') : detailTestData.keywords}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);

    // 断言是否全部通过
    expect(result.success, `产业资讯详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 