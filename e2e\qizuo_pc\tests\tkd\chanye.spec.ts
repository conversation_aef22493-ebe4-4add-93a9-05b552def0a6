// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption } from "../../common/test-helpers";
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";
import * as fs from 'fs';
import * as path from 'path';

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'chanye';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站产业服务SEO验证', () => {
  // 在每个测试后保存SEO报告
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('产业服务-频道页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryServiceChannelPage;

    await page.goto(URL_CONFIG.industryService);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`产业服务频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `产业服务频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('产业服务-列表页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.industryServiceListPage;

    await page.goto(`${URL_CONFIG.industryService}/list`);
    await page.waitForLoadState('networkidle');
    await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`产业服务列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    console.log(`描述: ${testData.description}`);
    console.log(`URL: ${testData.url}`);
    expect(result.success, `产业服务列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('产业服务-列表页-所在地筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试所在地筛选产业服务列表页');

    const regions: FilterOption[] = [
      { name: '北京市', value: '北京市' }, 
      { name: '上海市', value: '上海市' },
      { name: '广东省', value: '广东省' }
    ];
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.industryService}/list`,
      filterOptions: regions,
      getTestData: (option) => SEOTestData.industryServiceListPageRegionFilter(option.value), 
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"所在地选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('产业服务-详情页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试产业服务详情页');

    const companyNameInURL = "迪维珠宝（深圳）有限公司";
    await page.goto(`${URL_CONFIG.industryService}/detail/${encodeURIComponent(companyNameInURL)}`);
    await page.waitForLoadState('networkidle');

    // 等待3秒钟，确保页面内容完全加载和渲染
    console.log('等待3秒钟，确保页面完全加载...');
    await page.waitForTimeout(3000);
    console.log('页面已加载完成。');
    (testInfo as any).currentPageForReport = page;

    const currentUrl = page.url();
    const companyId = currentUrl.split('/').pop()?.split('?')[0] || 'company123'; 
    const companyNameForTestData = companyNameInURL; 

    const detailTestData = SEOTestData.industryServiceDetailPage(companyNameForTestData, companyId);
    
    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: detailTestData.title,
      keywords: Array.isArray(detailTestData.keywords) ? detailTestData.keywords : [detailTestData.keywords].filter(Boolean),
      description: detailTestData.description,
      url: detailTestData.url
    });
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    console.log(`产业服务详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`标题: ${detailTestData.title}`);
    console.log(`关键词: ${Array.isArray(detailTestData.keywords) ? detailTestData.keywords.join(', ') : detailTestData.keywords}`);
    console.log(`描述: ${detailTestData.description}`);
    console.log(`URL: ${detailTestData.url}`);
    expect(result.success, `产业服务详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 