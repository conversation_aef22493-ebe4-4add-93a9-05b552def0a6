// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";
import { testSEOWithFilter, formatErrors, type FilterOption } from "../../common/test-helpers";
import { chineseToPinyin } from "../../common/pinyin-utils";
import * as fs from 'fs';
import * as path from 'path';

// 当前文件的测试结果集合
let currentFileReportItems: SEOReportItem[] = [];
const SOURCE_FILE = 'zhaoshang-index.spec.ts';

test.describe('企座网站招商服务首页SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('招商服务-频道页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
      const seoValidator = new SEOValidatorPage(page);
      const testData = SEOTestData.zhaoshangServiceChannelPage;

      await page.goto(URL_CONFIG.zhaoshang);
      await page.waitForLoadState('networkidle');
      await page.evaluate(() => window.scrollBy(0, 300));

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
      (testInfo as any).seoTestData = testData;
      (testInfo as any).seoValidationResult = result;

      console.log(`招商服务频道页SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
      }
      console.log('验证的TDK信息:');
      console.log(`标题: ${testData.title}`);
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
      console.log(`描述: ${testData.description}`);
      console.log(`URL: ${testData.url}`);
      expect(result.success, `招商服务频道页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 