// @ts-check
import type { Page } from '@playwright/test';
import type { PlayWrightAiFixtureType } from '@midscene/web/playwright';

/**
 * 页面对象模型基类
 * 封装所有页面对象类共有的属性和方法，减少代码重复
 */
export class BasePage {
  protected readonly page: Page;
  public readonly ai: PlayWrightAiFixtureType['ai'];
  public readonly aiTap: PlayWrightAiFixtureType['aiTap'];
  public readonly aiInput: PlayWrightAiFixtureType['aiInput'];
  public readonly aiAssert: PlayWrightAiFixtureType['aiAssert'];
  public readonly aiHover: PlayWrightAiFixtureType['aiHover'];
  public readonly aiQuery: PlayWrightAiFixtureType['aiQuery'];
  public readonly aiWaitFor: PlayWrightAiFixtureType['aiWaitFor'];

  /**
   * 构造函数
   * @param page Playwright页面对象
   * @param aiFixtures AI工具集
   */
  constructor(page: Page, aiFixtures: PlayWrightAiFixtureType) {
    this.page = page;
    this.ai = aiFixtures.ai;
    this.aiTap = aiFixtures.aiTap;
    this.aiInput = aiFixtures.aiInput;
    this.aiAssert = aiFixtures.aiAssert;
    this.aiHover = aiFixtures.aiHover;
    this.aiQuery = aiFixtures.aiQuery;
    this.aiWaitFor = aiFixtures.aiWaitFor;
  }

  /**
   * 检查AI功能是否可用
   */
  protected checkAiAvailability(): asserts this is this & { ai: NonNullable<PlayWrightAiFixtureType['ai']> } {
    if (!this.ai) {
      throw new Error('AI功能不可用。请确保在创建页面对象时提供了aiFixtures参数。');
    }
  }

  /**
   * 检查AI点击功能是否可用
   */
  protected checkAiTapAvailability(): asserts this is this & { aiTap: NonNullable<PlayWrightAiFixtureType['aiTap']> } {
    if (!this.aiTap) {
      throw new Error('AI点击功能不可用。请确保在创建页面对象时提供了aiFixtures参数。');
    }
  }

  /**
   * 检查AI输入功能是否可用
   */
  protected checkAiInputAvailability(): asserts this is this & { aiInput: NonNullable<PlayWrightAiFixtureType['aiInput']> } {
    if (!this.aiInput) {
      throw new Error('AI输入功能不可用。请确保在创建页面对象时提供了aiFixtures参数。');
    }
  }

  /**
   * 检查AI断言功能是否可用
   */
  protected checkAiAssertAvailability(): asserts this is this & { aiAssert: NonNullable<PlayWrightAiFixtureType['aiAssert']> } {
    if (!this.aiAssert) {
      throw new Error('AI断言功能不可用。请确保在创建页面对象时提供了aiFixtures参数。');
    }
  }

  /**
   * 检查AI悬停功能是否可用
   */
  protected checkAiHoverAvailability(): asserts this is this & { aiHover: NonNullable<PlayWrightAiFixtureType['aiHover']> } {
    if (!this.aiHover) {
      throw new Error('AI悬停功能不可用。请确保在创建页面对象时提供了aiFixtures参数。');
    }
  }

  /**
   * 检查AI查询功能是否可用
   */
  protected checkAiQueryAvailability(): asserts this is this & { aiQuery: NonNullable<PlayWrightAiFixtureType['aiQuery']> } {
    if (!this.aiQuery) {
      throw new Error('AI查询功能不可用。请确保在创建页面对象时提供了aiFixtures参数。');
    }
  }

  /**
   * 检查AI等待功能是否可用
   */
  protected checkAiWaitForAvailability(): asserts this is this & { aiWaitFor: NonNullable<PlayWrightAiFixtureType['aiWaitFor']> } {
    if (!this.aiWaitFor) {
      throw new Error('AI等待功能不可用。请确保在创建页面对象时提供了aiFixtures参数。');
    }
  }

  /**
   * 检查AI功能是否可用
   */
  protected isAiAvailable(): boolean {
    return !!this.ai;
  }

  /**
   * 检查AI点击功能是否可用
   */
  protected isAiTapAvailable(): boolean {
    return !!this.aiTap;
  }
}