// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption, safeAIQuery } from "../../common/test-helpers";
import * as fs from 'fs';
import * as path from 'path';
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'xiangmu';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站找项目SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('找项目-基本列表页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试基本找项目列表页');
    const baseTestData = SEOTestData.projectListPage;

    await page.goto(URL_CONFIG.project);
    await page.waitForLoadState('networkidle');

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: baseTestData.title,
      keywords: Array.isArray(baseTestData.keywords) ? baseTestData.keywords : [baseTestData.keywords].filter(Boolean),
      description: baseTestData.description,
      url: baseTestData.url
    });
    (testInfo as any).seoTestData = baseTestData;
    (testInfo as any).seoValidationResult = result;

    console.log(`基本找项目列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`关键词: ${Array.isArray(baseTestData.keywords) ? baseTestData.keywords.join(', ') : baseTestData.keywords}`);
    expect(result.success, `基本找项目列表页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  test('找项目-单个行业筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试单个行业筛选找项目列表页');

    const industries: FilterOption[] = [
      { name: '农业行业', value: '农业' },
      { name: '建筑行业', value: '建筑' }
    ];
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: URL_CONFIG.project,
      filterOptions: industries,
      getTestData: (option) => SEOTestData.projectListPageIndustryFilterForTest(option.value),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"行业分类选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title
    });

    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('找项目-多行业组合筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试多行业组合筛选找项目列表页');

    const allErrors: {type: string, errors: string[]}[] = [];
    const multiIndustries = [['农业', '医疗健康']];

    for (const industryGroup of multiIndustries) {
      console.log(`测试多行业组合: ${industryGroup.join(', ')}`);
      await page.goto(URL_CONFIG.project);
      await page.waitForLoadState('networkidle', { timeout: 60000 });

      for (const industryName of industryGroup) {
        await aiTap(`"${industryName}"行业分类选项`);
        await page.waitForLoadState('networkidle');
      }

      const industryTestData = SEOTestData.projectListPageIndustryFilterForTest(industryGroup);
      const result = await seoValidator.validateTDKWithSoftAssertions({
        title: industryTestData.title,
        keywords: Array.isArray(industryTestData.keywords) ? industryTestData.keywords : [industryTestData.keywords].filter(Boolean),
        description: industryTestData.description,
        url: industryTestData.url
      });

      const reportItem: SEOReportItem = {
        testCaseTitle: `${testInfo.title} - ${industryGroup.join('+')}组合`,
        status: result.success ? 'Pass' : 'Fail',
        expectedTitle: industryTestData.title,
        actualTitle: result.actualElements.title,
        expectedKeywords: Array.isArray(industryTestData.keywords) ? industryTestData.keywords.join(', ') : String(industryTestData.keywords || ''),
        actualKeywords: result.actualElements.keywords ? result.actualElements.keywords.split(/[\s,]+/).map(k => k.trim()).filter(k => k).join(', ') : '',
        expectedDescription: industryTestData.description,
        actualDescription: result.actualElements.description,
        url: result.actualElements.url,
        expectedUrl: industryTestData.url,
        errors: result.errors.join('; ')
      };
      currentFileReportItems.push(reportItem);

      console.log(`${industryGroup.join('+')}行业组合筛选找项目列表页SEO验证结果: ${result.success ? '通过' : '失败'}`);
      if (result.errors.length > 0) {
        console.log(`失败项: ${result.errors.join(', ')}`);
        allErrors.push({
          type: `${industryGroup.join('+')}行业组合筛选找项目列表页`,
          errors: result.errors
        });
      }

      for (const industryName of industryGroup) {
        await aiTap(`"${industryName}"行业分类选项`);
        await page.waitForLoadState('networkidle');
      }
    }
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('找项目-单级地区筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试单级地区筛选找项目列表页');

    const regions: FilterOption[] = [
      { name: '浙江省区域', value: '浙江省' }
    ];
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: URL_CONFIG.project,
      filterOptions: regions,
      getTestData: (option) => SEOTestData.projectListPageRegionFilter(option.value),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"国家地区选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title
    });

    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('找项目-详情页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试项目详情页');

    await page.goto(`${URL_CONFIG.project}/detail/1729384254117584897`);
    await page.waitForLoadState('networkidle');

    // 等待3秒钟，确保页面内容完全加载和渲染
    console.log('等待3秒钟，确保页面完全加载...');
    await page.waitForTimeout(3000);
    console.log('页面已加载完成。');
    (testInfo as any).currentPageForReport = page;

    const currentUrl = page.url();
    const projectId = currentUrl.split('/').pop()?.split('?')[0] || 'project123';
    const detailTestData = SEOTestData.projectDetailPage(
      '康小虎·健康小屋',
      projectId,
      '北京市',
      '医疗健康',
      '30万元'
    );

    const result = await seoValidator.validateTDKWithSoftAssertions(detailTestData);
    (testInfo as any).seoTestData = detailTestData;
    (testInfo as any).seoValidationResult = result;

    console.log(`项目详情页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    expect(result.success, `项目详情页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 