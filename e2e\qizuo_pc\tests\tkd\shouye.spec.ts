// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption, safeAIQuery } from "../../common/test-helpers";
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";
import * as fs from 'fs';
import * as path from 'path';

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'shouye';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站首页SEO验证', () => {
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('首页SEO元素验证', async ({ page }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    const testData = SEOTestData.homePage;

    await page.goto(URL_CONFIG.main);
    await page.waitForLoadState('networkidle');

    const result = await seoValidator.validateTDKWithSoftAssertions({
      title: testData.title,
      keywords: Array.isArray(testData.keywords) ? testData.keywords : [testData.keywords].filter(Boolean),
      description: testData.description,
      url: testData.url
    });
    (testInfo as any).seoTestData = testData;
    (testInfo as any).seoValidationResult = result;

    console.log(`首页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }
    console.log('验证的TDK信息:');
    console.log(`关键词: ${Array.isArray(testData.keywords) ? testData.keywords.join(', ') : testData.keywords}`);
    expect(result.success, `首页SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 