/**
 * 产业资讯SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class ZixunSEOData extends BaseSEOData {
  // 产业资讯-频道页
  static industryNewsChannelPage: SEOTestDataItem = {
    title: "7*24小时快讯 市场聚焦 市场洞察 商讯快报 - 企座",
    keywords: [
      "7*24小时快讯",
      "市场聚焦",
      "市场洞察",
      "商讯快报"
    ],
    description: "产业资讯频道实时追踪不同产业政策新动向、市场新机会、行业新突破，助力企业把握战略先机。",
    url: "zixun.aitojoy.com"
  };

  // 产业资讯-列表页-7*24
  static industryNews24HListPage: SEOTestDataItem = {
    title: "7*24实时更新产业快讯 - 企座",
    keywords: [
      "行业新动态资讯",
      "产业新动向资讯"
    ],
    description: "第一时间推送行业新动态、产业新动向。",
    url: "zixun.aitojoy.com/kuaixun"
  };

  // 产业资讯-列表页-市场聚焦
  static industryNewsFocusListPage: SEOTestDataItem = {
    title: "行业内参 产业发展调研 - 企座",
    keywords: [
      "行业洞察内参",
      "产业发展调研"
    ],
    description: "针对近期产业发展新动向，提供深度洞察内参。",
    url: "zixun.aitojoy.com/jujiao"
  };

  // 产业资讯-列表页-市场洞察
  static industryNewsInsightListPage: SEOTestDataItem = {
    title: "产业新政策 国家惠企新举措 - 企座",
    keywords: [
      "国家产业布局新动向",
      "行业政策",
      "惠企政策"
    ],
    description: "聚合最新国家产业布局、政策动向信息，助您抓住先机。",
    url: "zixun.aitojoy.com/dongcha"
  };

  // 产业资讯-列表页-商讯快报
  static industryNewsBusinessListPage: SEOTestDataItem = {
    title: "创业项目调研 商机资讯 - 企座",
    keywords: [
      "创业项目调研",
      "最新商机资讯"
    ],
    description: "深度调研明星创业项目，足不出户一网打进商机。",
    url: "zixun.aitojoy.com/shangxunkuaibao"
  };

  /**
   * 产业资讯-详情页
   * @param channelName 资讯频道名称
   * @param newsTitle 资讯标题
   * @param newsId 资讯ID
   * @returns SEO数据对象
   */
  static industryNewsDetailPage = (
    channelName: string,
    newsTitle: string,
    newsId: string
  ) => {
    return {
      title: `${channelName}资讯${newsTitle}`,
      keywords: [
        `${channelName}详细内容`
      ],
      description: `${channelName}资讯${newsTitle}`,
      url: `zixun.aitojoy.com/detail/${newsId}`
    };
  };
} 