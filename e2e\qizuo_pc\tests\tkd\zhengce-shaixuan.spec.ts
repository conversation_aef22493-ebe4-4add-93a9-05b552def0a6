// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption, safeAIQuery } from "../../common/test-helpers";
import { chineseToPinyin } from "../../common/pinyin-utils";
import * as fs from 'fs';
import * as path from 'path';
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'zhengce-shaixuan';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站惠企政策筛选SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  test('惠企政策-列表页-奖补政策tab-发布地区筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试奖补政策-发布地区筛选');
    const regionData = [
      { name: '贵州省', value: '贵州' },
      { name: '上海市', value: '上海' }
    ];
    const regions: FilterOption[] = regionData.map(item => ({
      ...item,
      pinyin: chineseToPinyin(item.name) as string
    }));
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.policy}/jiangbu`,
      filterOptions: regions,
      getTestData: (option) => SEOTestData.policyRewardListPageRegionFilter(option.value, option.pinyin || ''),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"发布地区选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('惠企政策-列表页-奖补政策tab-政策类型筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试奖补政策-政策类型筛选');
    const policyTypeNames = ['监督管理', '产业补贴'];
    const policyTypes: FilterOption[] = policyTypeNames.map(name => ({
      name,
      value: name,
      pinyin: chineseToPinyin(name) as string
    }));
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.policy}/jiangbu`,
      filterOptions: policyTypes,
      getTestData: (option) => SEOTestData.policyRewardListPageTypeFilter(option.value, option.pinyin || ''),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"政策类型选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('惠企政策-列表页-奖补政策tab-组合筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试奖补政策-组合筛选');
    const comboFilters: FilterOption[] = [
      {
        name: '上海+产业补贴',
        value: JSON.stringify({ region: '上海', policyType: '产业补贴' }),
        pinyin: JSON.stringify({ regionPinyin: 'shanghai', policyTypePinyin: 'chanyebutie'})
      }
    ];

    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.policy}/jiangbu`,
      filterOptions: comboFilters,
      getTestData: (option) => {
        const { region, policyType } = JSON.parse(option.value);
        const { regionPinyin, policyTypePinyin } = JSON.parse(option.pinyin || '{}');
        return SEOTestData.policyRewardListPageCombinedFilter(region, policyType, regionPinyin, policyTypePinyin);
      },
      tapFilter: async (option) => {
        const { region, policyType } = JSON.parse(option.value);
        await aiTap(`"${region}"发布地区选项`);
        await page.waitForLoadState('networkidle');
        await aiTap(`"${policyType}"政策类型选项`);
        await page.waitForLoadState('networkidle');
      },
      cleanupFilter: async(option) => {
        const { region, policyType } = JSON.parse(option.value);
        await aiTap(`"${region}"发布地区选项`);
        await page.waitForLoadState('networkidle');
        await aiTap(`"${policyType}"政策类型选项`);
        await page.waitForLoadState('networkidle');
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('惠企政策-列表页-热点政策tab-发布地区筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试热点政策-发布地区筛选');
    const regionData = [
      { name: '上海市', value: '上海' }
    ];
    const regions: FilterOption[] = regionData.map(item => ({
      ...item,
      pinyin: chineseToPinyin(item.value) as string
    }));
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.policy}/redian`,
      filterOptions: regions,
      getTestData: (option) => SEOTestData.policyHotListPageRegionFilter(option.value, option.pinyin || ''),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"发布地区选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('惠企政策-列表页-热点政策tab-适用行业筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试热点政策-适用行业筛选');
    const industryNames = ['现代农业', '文化娱乐'];
    const industries: FilterOption[] = industryNames.map(name => ({
      name,
      value: name,
      pinyin: chineseToPinyin(name) as string
    }));
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.policy}/redian`,
      filterOptions: industries,
      getTestData: (option) => SEOTestData.policyHotListPageIndustryFilter(option.value, option.pinyin || ''),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"适用行业选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });

  test('惠企政策-列表页-热点政策tab-政策类型筛选SEO元素验证', async ({ page, aiTap }, testInfo: TestInfo) => {
    const seoValidator = new SEOValidatorPage(page);
    console.log('测试热点政策-政策类型筛选');
    const policyTypeNames = ['产业补贴', '监督管理'];
    const policyTypes: FilterOption[] = policyTypeNames.map(name => ({
      name,
      value: name,
      pinyin: chineseToPinyin(name) as string
    }));
    
    const allErrors = await testSEOWithFilter({
      page,
      seoValidator,
      baseUrl: `${URL_CONFIG.policy}/redian`,
      filterOptions: policyTypes,
      getTestData: (option) => SEOTestData.policyHotListPageTypeFilter(option.value, option.pinyin || ''),
      tapFilter: async (option) => {
        await aiTap(`"${option.value}"政策类型选项`);
      },
      currentFileReportItems: currentFileReportItems,
      baseTestCaseTitle: testInfo.title,
      testInfo: testInfo
    });
    expect(allErrors.length, formatErrors(allErrors)).toBe(0);
  });
}); 