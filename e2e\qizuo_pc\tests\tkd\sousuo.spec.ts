// @ts-check
import { test } from "../../fixture";
import { expect, type TestInfo } from "@playwright/test";
import { SEOValidatorPage } from "../../pages/seo-validator.page";
import { SEOTestData } from "../../data/seo-test-data";
import { URL_CONFIG, type SEOReportItem } from "../../common/base-config";
import { testSEOWithFilter, formatErrors, FilterOption, safeAIQuery } from "../../common/test-helpers";
import { chineseToPinyin } from '../../common/pinyin-utils';
import * as fs from 'fs';
import * as path from 'path';
import { handleSEOTestAfterEach, handleSEOTestAfterAll } from "../../common/seo-test-helper";

// 定义源文件名称，用于日志输出
const SOURCE_FILE = 'sousuo';
let currentFileReportItems: SEOReportItem[] = [];

test.describe('企座网站搜索SEO验证', () => {
  // 使用公共辅助函数处理afterEach
  test.afterEach(async ({ page }, testInfo: TestInfo) => {
    await handleSEOTestAfterEach(page, testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 使用公共辅助函数处理afterAll
  test.afterAll(async ({}, testInfo: TestInfo) => {
    await handleSEOTestAfterAll(testInfo, currentFileReportItems, SOURCE_FILE);
  });

  // 辅助函数：执行搜索和SEO验证
  async function validateSearchPageSEO({ page, ai, aiTap }, searchKeyword, searchType, testInfo) {
    const seoValidator = new SEOValidatorPage(page);
    const baseTestData = SEOTestData.searchPage;

    console.log(`测试搜索类型: ${searchType.name}`);
    await page.goto(URL_CONFIG.search);
    await page.waitForLoadState('networkidle');
    
    if (searchType.name !== '全站') {
      try {
        await aiTap(`搜索条上面的搜索类型选项卡"${searchType.tabText}"`);
        await page.waitForLoadState('networkidle');
      } catch (e) {
        console.error(`Error clicking tab for ${searchType.name}:`, e);
        throw new Error(`无法点击${searchType.name}搜索类型选项卡: ${e.message}`);
      }
    }

    await ai(`在搜索框内输入"${searchKeyword}"并触发搜索`);
    await page.waitForLoadState('networkidle');

    const expectedTitle = `${searchKeyword}企座${searchType.titleSuffix}`;
    const customTestData = {
      title: expectedTitle,
      keywords: Array.isArray(baseTestData.keywords) ? baseTestData.keywords : [baseTestData.keywords].filter(Boolean),
      description: baseTestData.description,
      url: `so.aitojoy.com/`
    };

    const result = await seoValidator.validateTDKWithSoftAssertions(customTestData);
    
    // 存储测试数据和结果，以便在afterEach中使用
    (testInfo as any).seoTestData = customTestData;
    (testInfo as any).seoValidationResult = result;

    console.log(`${searchType.name}搜索页SEO验证结果: ${result.success ? '通过' : '失败'}`);
    if (result.errors.length > 0) {
      console.log(`失败项: ${result.errors.join(', ')}`);
    }

    return result;
  }

  // 全站搜索测试
  test('全局搜索页-全站搜索SEO元素验证', async ({ page, ai, aiTap }, testInfo: TestInfo) => {
    const searchType = { name: '全站', tabText: '全站', titleSuffix: '搜索' };
    const searchKeyword = "人工智能";
    
    const result = await validateSearchPageSEO({ page, ai, aiTap }, searchKeyword, searchType, testInfo);
    expect(result.success, `全站搜索SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 项目搜索测试
  test('全局搜索页-项目搜索SEO元素验证', async ({ page, ai, aiTap }, testInfo: TestInfo) => {
    const searchType = { name: '项目', tabText: '项目', titleSuffix: '项目搜索' };
    const searchKeyword = "人工智能";
    
    const result = await validateSearchPageSEO({ page, ai, aiTap }, searchKeyword, searchType, testInfo);
    expect(result.success, `项目搜索SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 活动搜索测试
  test('全局搜索页-活动搜索SEO元素验证', async ({ page, ai, aiTap }, testInfo: TestInfo) => {
    const searchType = { name: '活动', tabText: '活动', titleSuffix: '活动搜索' };
    const searchKeyword = "人工智能";
    
    const result = await validateSearchPageSEO({ page, ai, aiTap }, searchKeyword, searchType, testInfo);
    expect(result.success, `活动搜索SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 企业搜索测试
  test('全局搜索页-企业搜索SEO元素验证', async ({ page, ai, aiTap }, testInfo: TestInfo) => {
    const searchType = { name: '企业', tabText: '企业', titleSuffix: '企业搜索' };
    const searchKeyword = "人工智能";
    
    const result = await validateSearchPageSEO({ page, ai, aiTap }, searchKeyword, searchType, testInfo);
    expect(result.success, `企业搜索SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 机构搜索测试
  test('全局搜索页-机构搜索SEO元素验证', async ({ page, ai, aiTap }, testInfo: TestInfo) => {
    const searchType = { name: '机构', tabText: '机构', titleSuffix: '机构搜索' };
    const searchKeyword = "人工智能";
    
    const result = await validateSearchPageSEO({ page, ai, aiTap }, searchKeyword, searchType, testInfo);
    expect(result.success, `机构搜索SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 人脉搜索测试
  test('全局搜索页-人脉搜索SEO元素验证', async ({ page, ai, aiTap }, testInfo: TestInfo) => {
    const searchType = { name: '人脉', tabText: '人脉', titleSuffix: '人脉搜索' };
    const searchKeyword = "人工智能";
    
    const result = await validateSearchPageSEO({ page, ai, aiTap }, searchKeyword, searchType, testInfo);
    expect(result.success, `人脉搜索SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 课程搜索测试
  test('全局搜索页-课程搜索SEO元素验证', async ({ page, ai, aiTap }, testInfo: TestInfo) => {
    const searchType = { name: '课程', tabText: '课程', titleSuffix: '课程搜索' };
    const searchKeyword = "人工智能";
    
    const result = await validateSearchPageSEO({ page, ai, aiTap }, searchKeyword, searchType, testInfo);
    expect(result.success, `课程搜索SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });

  // 政策搜索测试
  test('全局搜索页-政策搜索SEO元素验证', async ({ page, ai, aiTap }, testInfo: TestInfo) => {
    const searchType = { name: '政策', tabText: '政策', titleSuffix: '政策搜索' };
    const searchKeyword = "人工智能";
    
    const result = await validateSearchPageSEO({ page, ai, aiTap }, searchKeyword, searchType, testInfo);
    expect(result.success, `政策搜索SEO验证失败:\n${result.errors.join('\n')}`).toBeTruthy();
  });
}); 