/**
 * 企业服务SEO测试数据
 */
import { BaseSEOData, SEOTestDataItem } from '../base-seo-data';

export class QiyeSEOData extends BaseSEOData {
  // 企业服务-频道页
  static enterpriseServiceChannelPage: SEOTestDataItem = {
    title: "一站式企业服务平台_工商财税、知识产权、企业AI体检、企业实用AI智能体 - 企座",
    keywords: [
      "企业工商财税服务",
      "企业知识产权服务",
      "企业项目加速服务",
      "企业家健康出行服务",
      "企业AI体检",
      "企业实用AI智能体"
    ],
    description: "企座企业服务模块为创业公司提供工商注册、知识产权、项目加速等全生命周期服务。可通过AI大模型免费进行企业体检，同时为企业家提供健康出行服务，欢迎线上选择您需要的服务进行合作。",
    url: "qiyefuwu.aitojoy.com"
  };

  // 企业服务-列表页
  static enterpriseServiceListPage: SEOTestDataItem = {
    title: "企业一站式服务在线查询 订购服务 - 企座",
    keywords: [
      "企业工商财税服务在线订购",
      "企业知识产权服务在线订购",
      "企业项目加速服务在线订购",
      "企业家健康出行服务在线订购",
      "服务履约全流程在线管理"
    ],
    description: "超过30年经验的专业企业服务团队，提供超过500项企业服务。支持在线挑选与订购，帮助企业低成本高质量完成服务诉求。",
    url: "qiyefuwu.aitojoy.com/list"
  };

  /**
   * 企业服务-详情页
   * @param serviceCategory 企业服务分类
   * @param serviceName 企业服务名称
   * @param serviceId 服务ID
   * @param serviceIntro 服务优势介绍
   * @returns SEO数据对象
   */
  static enterpriseServiceDetailPage = (
    serviceCategory: string,
    serviceName: string,
    serviceId: string,
    serviceIntro: string = ''
  ) => {
    return {
      title: `${serviceCategory}精选企业服务项目${serviceName} - 企座`,
      keywords: [
        `${serviceCategory}精选服务在线订购`
      ],
      description: serviceIntro || `${serviceName}服务优势介绍`,
      url: `qiyefuwu.aitojoy.com/detail/${serviceId}`    
    };
  };
} 