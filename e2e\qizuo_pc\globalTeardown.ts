import * as fs from 'fs';
import * as path from 'path';
import type { SEOReportItem } from './common/base-config'; // Adjusted path assuming it's relative to this new file

const TMP_DATA_DIR = path.join(process.cwd(), 'reports', 'tmp_seo_data');
const FINAL_REPORT_DIR = path.join(process.cwd(), 'reports', 'seo-reports');
const FINAL_REPORT_FILENAME = 'all_seo_summary_report.csv';

/**
 * 确保指定目录存在，若不存在则递归创建。
 * @param dirPath 需要检查或创建的目录路径
 * @returns void
 */
function ensureDirExists(dirPath: string) {
  /**
   * 检查目录是否存在，若不存在则递归创建
   */
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

// Function to escape CSV cell data (copied from previous implementation)
/**
 * 转义CSV单元格数据，处理包含特殊字符的字符串
 * @param data 需要转义的原始数据，可能是null或undefined
 * @returns 处理后的CSV安全字符串
 * - 当数据为null/undefined时返回空字符串
 * - 包含逗号、双引号或换行符时会自动添加双引号包裹并转义内部双引号
 */
const escapeCsvCell = (data: string | null | undefined): string => {
  // 处理空值情况
  if (data === null || data === undefined) return '';
  let str = String(data);
  
  // 检测需要转义的特殊字符（逗号、双引号、换行符）
  if (str.includes(',') || str.includes('" ') || str.includes('\n')) {
    // 使用双引号包裹并转义内部双引号
    str = '"' + str.replace(/"/g, '""') + '"';
  }
  return str;
};

async function globalTeardown(): Promise<void> {
  console.log('[SEO GlobalTeardown] Starting to generate consolidated SEO report...');
  ensureDirExists(FINAL_REPORT_DIR);

  let allReportItems: SEOReportItem[] = [];
  const processedTitles = new Map<string, boolean>();

  if (!fs.existsSync(TMP_DATA_DIR)) {
    console.warn(`[SEO GlobalTeardown] Temporary data directory not found: ${TMP_DATA_DIR}. No report will be generated.`);
  } else {
    try {
      const tempFiles = fs.readdirSync(TMP_DATA_DIR).filter(file => file.endsWith('.json'));
      console.log(`[SEO GlobalTeardown] Found ${tempFiles.length} temporary JSON files in ${TMP_DATA_DIR}.`);

      // 首先收集所有测试结果
      let allCollectedItems: SEOReportItem[] = [];
      
      for (const tempFile of tempFiles) {
        const filePath = path.join(TMP_DATA_DIR, tempFile);
        try {
          const fileContent = fs.readFileSync(filePath, 'utf-8');
          const itemsFromFile = JSON.parse(fileContent) as SEOReportItem[];
          if (Array.isArray(itemsFromFile)) {
            allCollectedItems.push(...itemsFromFile);
          }
        } catch (err) {
          console.error(`[SEO GlobalTeardown] Error processing temporary file ${filePath}:`, err);
        }
      }
      
      // 规范化测试标题，去掉包含连续下划线的测试标题（这些是转义生成的）
      // 对测试用例进行分类和去重
      const testCaseGroups = new Map<string, SEOReportItem[]>();
      
      for (const item of allCollectedItems) {
        // 跳过标题中包含下划线占位符的测试结果
        if (item.testCaseTitle.includes('___')) {
          console.log(`[SEO GlobalTeardown] 跳过含有下划线占位符的测试项: ${item.testCaseTitle}`);
          continue; 
        }
        
        // 提取基础测试标题和参数部分
        const parts = item.testCaseTitle.split(' - ');
        const baseTitle = parts[0];
        const param = parts.length > 1 ? parts[1] : '';
        
        // 为每个基础测试用例创建一个分组
        if (!testCaseGroups.has(baseTitle)) {
          testCaseGroups.set(baseTitle, []);
        }
        testCaseGroups.get(baseTitle)!.push(item);
      }
      
      // 处理每个测试用例分组
      for (const [baseTitle, items] of testCaseGroups.entries()) {
        // 如果分组只有一个没有参数的测试结果，保留它
        if (items.length === 1) {
          allReportItems.push(items[0]);
          continue;
        }
        
        // 获取带参数的测试结果
        const parameterizedItems = items.filter(item => item.testCaseTitle.includes(' - '));
        if (parameterizedItems.length === 0) {
          // 如果没有参数化的测试结果，保留第一个
          allReportItems.push(items[0]);
          console.log(`[SEO GlobalTeardown] 为测试组 ${baseTitle} 保留了 1 个非参数化测试项`);
          continue;
        }
        
        // 对于带参数的测试结果，按URL进行去重
        const uniqueUrlItems = new Map<string, SEOReportItem[]>();
        parameterizedItems.forEach(item => {
          const url = item.url;
          if (!uniqueUrlItems.has(url)) {
            uniqueUrlItems.set(url, []);
          }
          uniqueUrlItems.get(url)!.push(item);
        });
        
        // 从每个URL组中选择标题最完整的测试结果（带特殊字符的）
        const deduplicatedItems: SEOReportItem[] = [];
        uniqueUrlItems.forEach((urlItems, url) => {
          // 选择最长的标题，或者包含更多特殊字符（如㎡）的标题
          const bestItem = urlItems.reduce((best, current) => {
            const bestSpecialCharCount = (best.testCaseTitle.match(/[^\w\s-]/g) || []).length;
            const currentSpecialCharCount = (current.testCaseTitle.match(/[^\w\s-]/g) || []).length;
            
            // 优先选择包含特殊字符的测试项
            if (currentSpecialCharCount > bestSpecialCharCount) return current;
            if (bestSpecialCharCount > currentSpecialCharCount) return best;
            
            // 如果特殊字符数相同，选择更长的测试标题
            return current.testCaseTitle.length > best.testCaseTitle.length ? current : best;
          });
          deduplicatedItems.push(bestItem);
        });
        
        allReportItems.push(...deduplicatedItems);
        console.log(`[SEO GlobalTeardown] 为测试组 ${baseTitle} 保留了 ${deduplicatedItems.length} 个去重后的参数化测试项（原始数: ${parameterizedItems.length}）`);
      }
      
      console.log(`[SEO GlobalTeardown] Total report items collected (after intelligent deduplication): ${allReportItems.length}`);

      // 可选：清理临时目录
      if (tempFiles.length > 0) {
        // fs.rmSync(TMP_DATA_DIR, { recursive: true, force: true }); // 谨慎使用
        // console.log(`[SEO GlobalTeardown] Temporary data directory ${TMP_DATA_DIR} cleaned up.`);
      }

    } catch (err) {
      console.error(`[SEO GlobalTeardown] Error reading temporary data directory ${TMP_DATA_DIR}:`, err);
    }
  }

  if (allReportItems.length === 0) {
    console.log('[SEO GlobalTeardown] No report items collected. Generating an empty or header-only report.');
    // 决定是否创建一个只有头部的空文件
  }

  const header = 'Test Case Title,Status,Expected Title,Actual Title,Expected Keywords,Actual Keywords,Expected Description,Actual Description,URL,Expected URL,Errors\n';
  const rows = allReportItems.map(item => {
    return [
      escapeCsvCell(item.testCaseTitle),
      escapeCsvCell(item.status),
      escapeCsvCell(item.expectedTitle),
      escapeCsvCell(item.actualTitle),
      escapeCsvCell(item.expectedKeywords),
      escapeCsvCell(item.actualKeywords),
      escapeCsvCell(item.expectedDescription),
      escapeCsvCell(item.actualDescription),
      escapeCsvCell(item.url),
      escapeCsvCell(item.expectedUrl),
      escapeCsvCell(item.errors)
    ].join(',');
  }).join('\n');

  const csvContentWithBOM = '\uFEFF' + header + rows;
  const finalReportPath = path.join(FINAL_REPORT_DIR, FINAL_REPORT_FILENAME);

  try {
    fs.writeFileSync(finalReportPath, csvContentWithBOM, { encoding: 'utf8' });
    console.log(`[SEO GlobalTeardown] Consolidated SEO report saved to: ${finalReportPath}`);
  } catch (error) {
    console.error(`[SEO GlobalTeardown] Failed to save consolidated SEO report to ${finalReportPath}:`, error);
  }
}

export default globalTeardown; 