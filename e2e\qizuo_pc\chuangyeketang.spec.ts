// @ts-check
import { test } from "./fixture";
import dotenv from "dotenv";
import { expect } from "@playwright/test";
import { ChuangYeKeTangPage } from "./pages/chuangye-ketang.page";

// 加载环境变量
import { URL_CONFIG } from "./common/base-config";

test.beforeEach(async ({ page }) => {
  await page.goto(URL_CONFIG.main);
});

test('创业课堂导航与选择', async ({ page, ai, aiTap, aiHover, aiWaitFor}) => {
  // 创建创业课堂页面对象，传入AI驱动功能
  const chuangyePage = new ChuangYeKeTangPage(page, { ai, aiTap, aiHover, aiWaitFor} as any);

  // 使用AI驱动方法导航到创业课堂
  await chuangyePage.hoverqiyefuwu();
  await chuangyePage.dianJiChuangYeKeTang();

  // 选择企业家充电课堂
  await chuangyePage.xuanZeChongDianKeTang();

  // 点击独家专栏
  await chuangyePage.dianJiZhuanLan('独家专栏');

  // 选择企业培训体系课程
  await chuangyePage.xuanZePeiXunXiTong();

  // 监听新页面打开事件，并执行点击操作
  const [newPage] = await Promise.all([
    page.context().waitForEvent('page'),
    chuangyePage.congpindaodaoliebcao()
  ]);

  // 等待新页面加载完成
  await newPage.waitForLoadState('domcontentloaded');
  await newPage.waitForTimeout(5000);

  console.log('新页面已打开并加载完成');

  // 在新页面上使用传统的断言方法，因为AI fixture绑定到原页面
  // 使用更宽松的选择器来查找搜索相关元素
  const searchElements = newPage.locator('input[type="search"], input[placeholder*="搜索"], input[placeholder*="search"], .search-input, [class*="search"], [id*="search"]');
  await expect(searchElements.first()).toBeVisible({ timeout: 10000 });

});
