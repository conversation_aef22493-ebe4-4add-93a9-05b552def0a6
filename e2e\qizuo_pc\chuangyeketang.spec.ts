// @ts-check
import { test } from "./fixture";
import dotenv from "dotenv";
import { expect } from "@playwright/test";
import { ChuangYeKeTangPage } from "./pages/chuangye-ketang.page";

// 加载环境变量
import { URL_CONFIG } from "./common/base-config";

test.beforeEach(async ({ page }) => {
  await page.goto(URL_CONFIG.main);
});

test('创业课堂导航与选择', async ({ page, ai, aiTap, aiHover, aiWaitFor, aiAssert}) => {
  // 创建创业课堂页面对象，传入AI驱动功能
  const chuangyePage = new ChuangYeKeTangPage(page, { ai, aiTap, aiHover, aiWaitFor} as any);

  // 使用AI驱动方法导航到创业课堂
  await chuangyePage.hoverqiyefuwu();
  await chuangyePage.dianJiChuangYeKeTang();

  // 选择企业家充电课堂
  await chuangyePage.xuanZeChongDianKeTang();

  // 点击独家专栏
  await chuangyePage.dianJiZhuanLan('独家专栏');

  // 选择企业培训体系课程
  await chuangyePage.xuanZePeiXunXiTong();

  // 监听新页面打开事件，并执行点击操作
  const [newPage] = await Promise.all([
    page.context().waitForEvent('page'),
    chuangyePage.congpindaodaoliebcao()
  ]);

  // 等待新页面加载完成
  await newPage.waitForLoadState('domcontentloaded');
  await newPage.waitForTimeout(2000);

  console.log('新页面已打开并加载完成');

  // 尝试使用AI断言验证新页面内容
  try {
    // 导入PlaywrightAiFixture来为新页面创建AI功能
    const { PlaywrightAiFixture } = await import('@midscene/web/playwright');

    // 为新页面创建AI fixture
    const newPageAiFixture = PlaywrightAiFixture();

    // 创建一个临时的测试上下文来使用AI功能
    const tempTest = {
      page: newPage,
      ...newPageAiFixture
    };

    // 使用AI断言验证新页面内容
    await tempTest.aiAssert('页面包含企业家充电课堂相关内容');

    console.log('AI断言成功执行');
  } catch (error) {
    console.log('AI断言失败，使用传统断言:', error);

    // 如果AI断言失败，回退到传统断言
    await expect(newPage.locator('text=企业家充电课堂')).toBeVisible({ timeout: 5000 });

    // 验证页面URL是否正确
    expect(newPage.url()).toContain('test-ketang.aitojoy.com');
  }

});
