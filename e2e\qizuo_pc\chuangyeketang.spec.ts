// @ts-check
import { test } from "./fixture";
import dotenv from "dotenv";
import { expect } from "@playwright/test";
import { ChuangYeKeTangPage } from "./pages/chuangye-ketang.page";

// 加载环境变量
import { URL_CONFIG } from "./common/base-config";

test.beforeEach(async ({ page }) => {
  await page.goto(URL_CONFIG.main);
});

test('创业课堂导航与选择', async ({ page, ai, aiTap, aiHover, aiWaitFor,aiAssert}) => {
  // 创建创业课堂页面对象，传入AI驱动功能
  const chuangyePage = new ChuangYeKeTangPage(page, { ai, aiTap, aiHover, aiWaitFor} as any);

  // 使用AI驱动方法导航到创业课堂
  await chuangyePage.hoverqiyefuwu();
  await chuangyePage.dianJiChuangYeKeTang();

  // 选择企业家充电课堂
  await chuangyePage.xuanZeChongDianKeTang();

  // 点击独家专栏
  await chuangyePage.dianJiZhuanLan('独家专栏');

  // 选择企业培训体系课程
  await chuangyePage.xuanZePeiXunXiTong();
  await chuangyePage.congpindaodaoliebcao();
  await page.waitForTimeout(3000);
  aiAssert('页面有搜索条')
  
});
