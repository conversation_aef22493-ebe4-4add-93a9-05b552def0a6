// @ts-check
import { test } from "./fixture";
import dotenv from "dotenv";
import { expect } from "@playwright/test";
import { ChuangYeKeTangPage } from "./pages/chuangye-ketang.page";

// 加载环境变量
import { URL_CONFIG } from "./common/base-config";

test.beforeEach(async ({ page }) => {
  await page.goto(URL_CONFIG.main);
});

test('创业课堂导航与选择', async ({ page, ai, aiTap, aiHover, aiWaitFor}) => {
  // 创建创业课堂页面对象，传入AI驱动功能
  const chuangyePage = new ChuangYeKeTangPage(page, { ai, aiTap, aiHover, aiWaitFor} as any);

  // 使用AI驱动方法导航到创业课堂
  await chuangyePage.hoverqiyefuwu();
  await chuangyePage.dianJiChuangYeKeTang();

  // 选择企业家充电课堂
  await chuangyePage.xuanZeChongDianKeTang();

  // 点击独家专栏
  await chuangyePage.dianJiZhuanLan('独家专栏');

  // 选择企业培训体系课程
  await chuangyePage.xuanZePeiXunXiTong();

  // 监听新页面打开事件，并执行点击操作
  const [newPage] = await Promise.all([
    page.context().waitForEvent('page'),
    chuangyePage.congpindaodaoliebcao()
  ]);

  // 等待新页面加载完成
  await newPage.waitForLoadState('domcontentloaded');
  await newPage.waitForTimeout(2000);

  console.log('新页面已打开并加载完成');

  // 在新页面上验证页面内容，确认成功导航到课程列表页面
  // 检查页面是否包含课程相关的内容
  await expect(newPage.locator('text=企业家充电课堂')).toBeVisible({ timeout: 5000 });

  // 验证页面URL是否正确
  expect(newPage.url()).toContain('test-ketang.aitojoy.com');

});
