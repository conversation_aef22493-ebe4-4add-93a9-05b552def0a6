# 企业服务平台自动化测试项目

[![Playwright](https://img.shields.io/badge/tested%20with-Playwright-2EAD33?logo=playwright)](https://playwright.dev/)
[![TypeScript](https://img.shields.io/badge/made%20with-TypeScript-3178C6?logo=typescript)](https://www.typescriptlang.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

## 1. 项目概述

本项目是基于 Playwright 的端到端自动化测试框架，用于测试**企业服务平台 (aitojoy.com)** 的各个子系统和功能。目前，项目的核心测试范围是其 **PC客户端 (qizuo_pc)**。

与传统基于选择器 (Selector) 的自动化测试不同，本项目深度集成了 **Midscene AI**。它利用先进的多模态AI模型，通过视觉理解来定位和交互界面元素。这极大地增强了测试脚本的健壮性，使其能够抵抗因前端代码重构或界面微调导致的选择器失效问题，显著提升了自动化测试的稳定性和开发效率。

## 2. 技术栈

* **测试框架**: Playwright
* **AI 辅助**: Midscene AI
* **语言**: TypeScript
* **环境管理**: dotenv
* **包管理器**: pnpm

## 3. 项目架构

```
midscene-playwright/
├── e2e/                          # 端到端测试目录
│   ├── qizuo_pc/                 # PC端测试
│   │   ├── config/               # 环境配置
│   │   ├── pages/                # 页面对象模型
│   │   ├── tests/                # 测试用例
│   │   ├── common/               # 通用功能
│   │   ├── data/                 # 测试数据
│   │   └── scripts/              # 辅助脚本
│   ├── Android/                  # Android端测试
│   └── aitojoy/                  # 其他测试项目
├── reports/                      # 测试报告
├── scripts/                      # 项目脚本
├── data/                         # 共享测试数据
├── playwright.config.ts          # Playwright配置
└── package.json                  # 项目依赖
```

## 4. 快速上手

### 4.1. 前置条件

在开始之前，请确保您的开发环境中已安装以下软件：

*   **Node.js**: v18.x 或更高版本
*   **pnpm**: `npm install -g pnpm`

### 4.2. 安装与配置

1.  **克隆项目**
    ```bash
    git clone http://***********/test_cases/midscene-playwright.git
    cd midscene-playwright
    ```

2.  **安装依赖**
    ```bash
    pnpm install
    ```

3.  **配置环境变量**

    复制项目根目录下的 `.env.example` 文件，并重命名为 `.env`。然后根据需要修改文件中的配置。

    **.env.example 模板:**
    ```shell
    # ================================================================
    # Midscene AI 模型配置
    # 需要使用多模态模型，例如 qwenvl 或 doubao1.5vl
    # 更多信息请参考文档: https://midscenejs.com/choose-a-model
    # ================================================================
    OPENAI_API_KEY="YOUR_API_KEY"
    # 示例：使用阿里云千问
    OPENAI_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
    MIDSCENE_MODEL_NAME=qwen-vl-plus-2025-05-07

    # ================================================================
    # 测试环境配置
    # 可选值: 'test' (测试环境) 或 'production' (生产环境)
    # ================================================================
    QIZUO_ENV=test

    # ================================================================
    # (可选) 直接指定测试URL
    # ================================================================
    # QIZUO_PC_URL="https://test-www.aitojoy.com"
    ```

## 5. 核心命令

### 5.1. 运行测试

*   **Web端测试**
    ```bash
    # 运行所有Web端测试
    pnpm run e2e

    # 使用缓存运行（提高后续运行速度）
    pnpm run e2e:cache

    # 启动 Playwright UI 调试模式
    pnpm run e2e:ui
    ```

*   **Android端测试**
    ```bash
    # 运行特定的Android测试脚本 (示例)
    pnpm run wecom
    ```

### 5.2. 更新认证信息

在运行测试前，特别是首次运行时，需要获取认证信息。

```bash
# 更新PC端认证信息
pnpm run update-auth:pc

# 更新管理后台认证信息
pnpm run update-auth:admin
```

## 6. 测试报告

测试完成后，将在控制台输出两个报告的路径：

1.  **Midscene Report**: 由 Midscene AI 生成的报告，侧重于 AI 操作分析。
2.  **Playwright Report**: 标准的 Playwright 测试报告，包含详细的执行步骤、Trace 和截图。

```
Midscene report: ./midscene_run/report/some_id.html
Playwright report: ./reports/playwright-report-some-timestamp/index.html
```

## 7. 设计模式与核心概念

### 7.1. 页面对象模型 (POM)

项目采用页面对象模型（POM）来组织UI交互逻辑，将页面元素和相关操作封装在独立的类中，以提高代码的可维护性和复用性。

```typescript
// 示例：人脉广场页面对象
const renMaiPage = new RenMaiGuangChangPage(page, { ai, aiTap, aiInput, aiAssert, aiWaitFor });

// 使用页面对象进行操作
await renMaiPage.searchConnection('企业家');
await renMaiPage.verifySearchResults('企业家');
```

### 7.2. AI 辅助功能

项目通过 Midscene AI 提供了多种AI驱动的测试能力，简化了复杂场景的自动化实现。

*   `aiTap`: AI 辅助视觉点击
*   `aiInput`: AI 辅助视觉输入
*   `aiAssert`: AI 辅助视觉断言
*   `aiQuery`: AI 辅助视觉查询
*   `aiWaitFor`: AI 辅助等待视觉条件

## 8. 如何贡献

欢迎为本项目贡献代码！请遵循以下基本流程：

1.  **分支管理**: 请从 `main` 分支创建新的特性分支 (`feature/your-feature`) 或修复分支 (`fix/your-bug`)。
2.  **代码风格**: 遵循项目已有的编码风格。在提交前，可运行代码检查工具（如有）。
3.  **提交PR**: 完成开发后，提交 Pull Request 到 `main` 分支，并简要描述您的修改内容。

## 9. 常见问题 (FAQ)

**Q1: 测试运行失败，提示"认证失败"或"未登录"怎么办？**

A1: 这是因为缺少有效的登录凭证。请在项目根目录运行 `pnpm run update-auth:pc` 命令来刷新认证信息，然后再重新运行测试。

**Q2: AI 相关功能（如 `aiTap`）不工作或报错，如何排查？**

A2: 请检查 `.env` 文件中的 AI 模型配置是否正确，特别是 `OPENAI_API_KEY`, `OPENAI_BASE_URL`, 和 `MIDSCENE_MODEL_NAME` 这三个字段。确保 API Key 有效且模型名称无误。

## 10. 参考文档

*   [Midscene与Playwright集成指南](https://midscenejs.com/integrate-with-playwright.html)
*   [Midscene API文档](https://midscenejs.com/api.html)
*   [如何为Midscene选择AI模型](https://midscenejs.com/choose-a-model)
*   [Playwright官方文档](https://playwright.dev/docs/intro)

