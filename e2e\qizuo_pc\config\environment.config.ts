// 使用集中环境配置模块，不再直接导入和配置dotenv
import '../../../config/env'; // 使用相对路径导入环境配置模块

/**
 * 环境配置管理类
 * 支持测试环境和生产环境的自动切换
 * 自动生成子域名URL，避免硬编码
 */
export class EnvironmentConfig {
  private environment: 'test' | 'production';
  private baseDomain: string;
  
  constructor() {
    // 通过环境变量控制环境，默认为测试环境
    this.environment = this.determineEnvironment();
    this.baseDomain = 'aitojoy.com';
  }

  /**
   * 确定当前运行环境
   */
  private determineEnvironment(): 'test' | 'production' {
    // 优先级：QIZUO_ENV > NODE_ENV > 默认test
    if (process.env.QIZUO_ENV === 'production') {
      return 'production';
    }
    if (process.env.NODE_ENV === 'production') {
      return 'production';
    }
    return 'test';
  }

  /**
   * 生成子域名URL
   * @param subdomain 子域名前缀
   * @returns 完整的URL
   */
  private generateUrl(subdomain: string): string {
    const prefix = this.environment === 'production' ? '' : 'test-';
    return `https://${prefix}${subdomain}.${this.baseDomain}`;
  }

  /**
   * 获取所有URL配置
   */
  getUrlConfig() {
    return {
      // 主站
      main: this.generateUrl('www'),
      // 搜索
      search: this.generateUrl('so'),
      // 找项目
      project: this.generateUrl('xiangmu'),
      // 聚活动
      activity: this.generateUrl('huodong'),
      // 人脉广场
      connection: this.generateUrl('renmai'),
      // 产业服务
      industryService: this.generateUrl('chanyefuwu'),
      // 惠企政策
      policy: this.generateUrl('zhengce'),
      // 企业诊断
      diagnosis: this.generateUrl('qiyezhenduan'),
      // 联营服务
      jointOperation: this.generateUrl('lianyingfuwu'),
      // 创业课堂
      course: this.generateUrl('ketang'),
      // 产业资讯
      news: this.generateUrl('zixun'),
      // 资金服务
      funding: this.generateUrl('zijinfuwu'),
      // 企业服务
      enterpriseService: this.generateUrl('qiyefuwu'),
      // AI落地页
      ai: this.generateUrl('ai'),
      // 招商服务
      zhaoshang: this.generateUrl('zhaoshang')
    };
  }

  /**
   * 动态生成任意子域名URL
   * @param subdomain 子域名前缀
   * @returns 完整的URL
   */
  generateCustomUrl(subdomain: string): string {
    return this.generateUrl(subdomain);
  }

  /**
   * 获取当前环境
   */
  getEnvironment(): 'test' | 'production' {
    return this.environment;
  }

  /**
   * 获取基础域名
   */
  getBaseDomain(): string {
    return this.baseDomain;
  }

  /**
   * 是否为生产环境
   */
  isProduction(): boolean {
    return this.environment === 'production';
  }

  /**
   * 是否为测试环境
   */
  isTest(): boolean {
    return this.environment === 'test';
  }

  /**
   * 获取环境前缀
   */
  getEnvironmentPrefix(): string {
    return this.environment === 'production' ? '' : 'test-';
  }

  /**
   * 打印当前配置信息
   */
  printConfig(): void {
    console.log('=== 环境配置信息 ===');
    console.log(`当前运行环境: ${this.environment}`);
    console.log(`基础域名: ${this.baseDomain}`);
    console.log(`环境前缀: ${this.getEnvironmentPrefix()}`);
    console.log(`主站URL: ${this.generateUrl('www')}`);
    console.log('==================');
  }
}

// 创建单例实例
export const envConfig = new EnvironmentConfig();

// 导出URL配置
export const URL_CONFIG = envConfig.getUrlConfig();

// 导出环境类型
export type Environment = 'test' | 'production';
