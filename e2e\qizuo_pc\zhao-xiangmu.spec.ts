// @ts-check
import { test } from "./fixture";
import { expect } from "@playwright/test";
import { ZhaoXiangMuPage } from "./pages/zhao-xiangmu.page";

// 导入URL配置
import { URL_CONFIG } from "./common/base-config";

test.beforeEach(async ({ page }) => {
  await page.goto(URL_CONFIG.main);
});

test('找项目页面基本导航与搜索测试', async ({ page, ai, aiTap, aiInput, aiAssert, aiHover, aiWaitFor }) => {
  // 创建找项目页面对象，传入AI驱动功能
  const zhaoXiangMuPage = new ZhaoXiangMuPage(page, { ai, aiTap, aiInput, aiAssert, aiHover, aiWaitFor } as any);

  // 导航到找项目页面
  await zhaoXiangMuPage.daohangDaoZhaoXiangMu();
  
  // 验证项目列表可见
  await zhaoXiangMuPage.yanzhengXiangmuLiebiaoKejian();
  
  // 搜索项目
  await zhaoXiangMuPage.sousuoXiangMu('健康');
  
  // 验证搜索结果
  await zhaoXiangMuPage.yanzhengSousuoJieguo('健康');
});

test('找项目筛选功能测试', async ({ page, ai, aiTap, aiInput, aiAssert, aiHover, aiWaitFor }) => {
  // 创建找项目页面对象，传入AI驱动功能
  const zhaoXiangMuPage = new ZhaoXiangMuPage(page, { ai, aiTap, aiInput, aiAssert, aiHover, aiWaitFor } as any);

  // 导航到找项目页面
  await zhaoXiangMuPage.daohangDaoZhaoXiangMu();
  
  // 选择行业分类
  await zhaoXiangMuPage.xuanzeHangyeFenlei('医疗健康');
  
  // 选择地区
  await zhaoXiangMuPage.xuanzeDiqu('北京市');
  
  // 选择融资轮次
  await zhaoXiangMuPage.xuanzeRongziLunci('A轮');
  
  // 选择合作金额
  await zhaoXiangMuPage.xuanzeHezuoJine('¥30-50万');
  
  // 验证筛选后的项目列表可见
  await zhaoXiangMuPage.yanzhengXiangmuLiebiaoKejian();
});

test('找项目详情查看测试', async ({ page, ai, aiTap, aiInput, aiAssert, aiHover, aiWaitFor }) => {
  // 创建找项目页面对象，传入AI驱动功能
  const zhaoXiangMuPage = new ZhaoXiangMuPage(page, { ai, aiTap, aiInput, aiAssert, aiHover, aiWaitFor } as any);

  // 导航到找项目页面
  await zhaoXiangMuPage.daohangDaoZhaoXiangMu();
  
  // 搜索项目
  await zhaoXiangMuPage.sousuoXiangMu('健康');
  
  // 点击第一个项目卡片
  await zhaoXiangMuPage.dianjiFenpinKa('康小虎·健康小屋');
  
  // 验证项目详情页面
  if (aiAssert) {
    await aiAssert('项目详情页面已正确加载');
  } else {
    // 检查页面标题或其他元素来验证详情页面已加载
    await expect(page.getByText('康小虎·健康小屋')).toBeVisible();
  }
});

test('找项目页面按钮交互测试', async ({ page, ai, aiTap, aiInput, aiAssert, aiHover, aiWaitFor }) => {
  // 创建找项目页面对象，传入AI驱动功能
  const zhaoXiangMuPage = new ZhaoXiangMuPage(page, { ai, aiTap, aiInput, aiAssert, aiHover, aiWaitFor } as any);

  // 导航到找项目页面
  await zhaoXiangMuPage.daohangDaoZhaoXiangMu();
  
  // 点击"我要加盟"按钮
  await zhaoXiangMuPage.dianjiWoyaoJiameng();
  
  // 验证加盟表单或对话框出现
  if (aiAssert) {
    await aiAssert('加盟表单或对话框已显示');
  } else {
    // 检查表单或对话框元素
    await expect(page.getByText('对接专属顾问')).toBeVisible();
  }
  
  // 返回找项目页面
  await page.goBack();
  await page.waitForLoadState('networkidle');
  
  // 点击"我有项目"按钮
  await zhaoXiangMuPage.dianjiWoyouXiangmu();
  
  // 验证项目提交表单或对话框出现
  if (aiAssert) {
    await aiAssert('项目提交表单或对话框已显示');
  } else {
    // 检查表单或对话框元素
    await expect(page.getByText('立即孵化加速')).toBeVisible();
  }
}); 