// @ts-check
import { test } from "./fixture";
import { expect } from "@playwright/test";
import { RenMaiGuangChangPage } from "./pages/renmai-guangchang.page";

test.beforeEach(async ({ page }) => {
  await page.goto(process.env.QIZUO_PC_URL!);
});

test('人脉广场基本功能测试', async ({ page, ai, aiTap, aiInput, aiAssert, aiWaitFor }) => {
  // 创建人脉广场页面对象，传入AI驱动功能
  const renMaiPage = new RenMaiGuangChangPage(page, { ai, aiTap, aiInput, aiAssert, aiWaitFor } as any);

  // 点击导航到人脉广场
  await aiTap('导航栏中的"人脉广场"链接');

  // 等待页面加载
  await aiWaitFor('人脉广场页面已完全加载', { timeoutMs: 5000 });

  // 验证页面标题
  await renMaiPage.verifyPageTitle('人脉广场');

  // 搜索人脉
  await renMaiPage.searchConnection('企业家');

  // 验证搜索结果
  await renMaiPage.verifySearchResults('企业家');

  // 点击筛选选项
  await renMaiPage.clickFilterOption('行业');

  // 验证人脉列表可见
  await renMaiPage.verifyConnectionListVisible();
});