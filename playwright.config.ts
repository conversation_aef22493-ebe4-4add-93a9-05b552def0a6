import { defineConfig, devices } from "@playwright/test";
// 导入集中的环境配置模块，确保环境变量只加载一次
import "./config/env";

/**
 * See https://playwright.dev/docs/test-configuration.
 */
// 创建时间戳用于报告文件夹命名
const timestamp = new Intl.DateTimeFormat('zh-CN', {
  year: 'numeric',
  month: '2-digit',
  day: '2-digit',
  hour: '2-digit',
  minute: '2-digit',
  second: '2-digit',
  hour12: false
}).format(new Date()).replace(/[\/:]/g, '-');

export default defineConfig({
  testDir: "./e2e",
  // 根据需要选择要运行的测试
  // 运行 Android 测试时使用: ["android/*.ts"]
  // 运行 Web 测试时使用: ["**/*.spec.ts"]
  testMatch: ["**/*.spec.ts"],
  timeout: 30 * 1000,
  /* Run tests in files in parallel */
  fullyParallel: true,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: Boolean(process.env.CI),
  /* Retry on both CI and local */
  retries: process.env.CI ? 2 : 0,
  /* Configure workers for optimal performance */
  // 当运行多个设备测试时，确保 workers 数量不超过可用设备数量
  workers: 4, // 限制为1个worker以避免设备资源竞争
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */

  reporter: [
    ['list'],
    ['html', {
      outputFolder: `reports/playwright-report-${timestamp}`,
      preserveOutput: true,
      outputDir: `reports/playwright-report-${timestamp}`
    }],
    ['@midscene/web/playwright-report']
  ],
  // reporter: [["list"], ["html"], ["@midscene/web/playwright-report"]],
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  globalSetup: './e2e/qizuo_pc/globalSetup.ts',
  globalTeardown: './e2e/qizuo_pc/globalTeardown.ts',
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    // baseURL: 'http://127.0.0.1:3000',
    /* Run tests in headful mode */
    headless: true,

    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: "retain-on-failure",
    /* Screenshot on failure */
    screenshot: "only-on-failure",
    /* Record video on failure */
    video: "retain-on-failure",
    /* Default viewport and window position */
    viewport: { width: 1920, height: 1080 },
    launchOptions: {
      args: ['--window-position=-1920,30','--window-size=1920,1080']
    },
  },

  /* Configure projects for major browsers with test groups */
  projects: [
    {
      name: "chromium",
      use: {
        ...devices["Desktop Chrome"],

      },
    },

  ],
});
