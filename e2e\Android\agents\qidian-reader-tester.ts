/**
 * 起点阅读测试代理
 * 
 * 封装所有与起点阅读App自动化任务相关的方法
 */

import { AndroidAgent } from '@midscene/android';
import { sleep, log } from '../utils/common';
import { checkShouldExit } from '../utils/exit-handler';

export class QidianReaderTester {
  private agent: AndroidAgent;

  constructor(agent: AndroidAgent) {
    this.agent = agent;
  }

  /**
   * 执行完整的起点阅读自动化流程
   */
  async runFullTest(): Promise<void> {
    log('开始起点阅读自动化任务...', 'info');
    
    await this.launchApp();
    await this.navigateToRewardPage();
    await this.handleLotteryTask();
    await this.handleVideoRewardTasks();
    await this.handleReadingPointsTask();
    
    log('所有起点阅读任务已完成！', 'success');
  }

  /**
   * 启动起点阅读应用
   */
  async launchApp(): Promise<void> {
    log('启动起点阅读应用...', 'info');
    await this.agent.launch('com.qidian.QDReader/.ui.activity.MainGroupActivity');
    log('起点阅读应用启动成功', 'success');
  }

  /**
   * 导航到福利页面
   */
  async navigateToRewardPage(): Promise<void> {
    log('尝试进入福利页面...', 'info');
    checkShouldExit();
    
    await this.agent.aiTap('"领福利"按钮或者"签到"按钮，精确匹配', { deepThink: true });
    await sleep(2000);

    // 确认是否在福利页面
    const isOnRewardPage = await this.agent.aiQuery('当前页面是否包含"激励视频任务"等文字');
    if (!isOnRewardPage) {
      log('未在福利页面，尝试重新导航...', 'info');
      await this.agent.aiTap('页面顶部的领福利或者签到按钮');
      await this.agent.aiWaitFor('福利页面加载完成', { timeoutMs: 5000 });
    }
    
    log('成功进入福利页面', 'success');
  }

  /**
   * 处理抽奖任务
   */
  async handleLotteryTask(): Promise<void> {
    log('检查抽奖任务...', 'info');
    checkShouldExit();
    
    const hasLotteryButton = await this.agent.aiQuery('页面是否有"抽奖机会"或"看视频得抽奖机会"相关按钮');

    if (!hasLotteryButton) {
      log('未找到抽奖按钮，跳过抽奖任务', 'info');
      return;
    }

    log('找到抽奖按钮，开始抽奖流程...', 'info');
    await this.agent.aiTap('抽奖按钮');

    // 循环抽奖直到没有机会
    let continueLoop = true;
    while (continueLoop) {
      checkShouldExit();

      // 检查是否还有抽奖机会
      const noMoreChance = await this.agent.aiQuery('页面是否显示"明天再来"');
      if (noMoreChance) {
        log('抽奖次数已用完', 'info');
        await this.agent.aiTap('关闭弹窗按钮或者X按钮');
        continueLoop = false;
        continue;
      }

      // 检查是否有看视频得抽奖机会的按钮
      const hasVideoButton = await this.agent.aiQuery('页面是否有"抽奖机会+1"相关按钮');
      if (hasVideoButton) {
        log('通过观看视频获取抽奖机会...', 'info');
        await this.agent.aiTap('抽奖机会+1的按钮');
        await this.watchVideoAd();
      } else {
        log('执行抽奖...', 'info');
        await this.agent.aiTap('抽奖按钮');
        await sleep(3000);
      }
    }

    log('抽奖任务完成', 'success');
    await sleep(2000);

    // 周日特殊处理：兑换章节卡
    await this.handleSundayExchange();
  }

  /**
   * 处理周日特殊兑换任务
   */
  private async handleSundayExchange(): Promise<void> {
    // 判断当前是否为周日
    const currentDay = new Date().getDay();
    if (currentDay !== 0) { // 0 表示周日
      log('今天不是周日，跳过兑换任务', 'info');
      return;
    }

    log('今天是周日，检查兑换任务...', 'info');
    checkShouldExit();
    
    // 检查页面是否有兑换按钮
    const hasExchangeButton = await this.agent.aiQuery('页面是否存在"去兑换"按钮');
    if (hasExchangeButton) {
      log('找到兑换按钮，开始兑换流程...', 'info');
      await this.agent.aiTap('去兑换按钮');
      await this.agent.ai('点击可兑换最大点数的章节卡的"兑换"按钮');
      await this.agent.aiTap('兑换按钮');

      // 处理可能出现的滑块验证
      if (await this.agent.aiQuery('页面是否存在图片滑块验证')) {
        log('检测到滑块验证，尝试完成验证...', 'info');
        await this.agent.ai('滑动图片滑块到验证通过');
      }

      log('兑换完成，等待确认...', 'info');
      await sleep(3000);
      await this.agent.aiTap('关闭弹窗按钮或者X按钮');
      log('周日特殊兑换任务完成', 'success');
    } else {
      log('未找到兑换按钮，可能已兑换或无可兑换项目', 'info');
    }
  }
  /**
   * 处理阅读积分领取任务
   */
  async handleReadingPointsTask(): Promise<void> {
    log('开始处理阅读积分领取任务...', 'info');
    checkShouldExit();
    
    // 检查是否有阅读积分可领取
    const hasReadingPoints = await this.agent.aiQuery('页面是否存在"领取阅读积分"按钮');
    
    if (hasReadingPoints) {
      log('找到可领取的阅读积分，开始领取...', 'info');
      // 点击领取阅读积分按钮
      await this.agent.aiTap('领取阅读积分按钮');
      await sleep(2000);
      
      // 处理可能出现的确认弹窗
      const hasConfirmDialog = await this.agent.aiQuery('页面是否存在确认领取的弹窗或按钮');
      if (hasConfirmDialog) {
        log('检测到确认弹窗，点击确认...', 'info');
        await this.agent.aiTap('确认按钮');
        await sleep(2000);
      }
      
      log('阅读积分领取成功', 'success');
    } else {
      log('未找到可领取的阅读积分，可能已领取或无积分可领', 'info');
    }
    
    await sleep(1000);
  }

  /**
   * 处理激励视频任务
   */
  async handleVideoRewardTasks(): Promise<void> {
    log('开始处理激励视频任务...', 'info');
    checkShouldExit();
    
    await this.agent.aiScroll({ direction: 'down', distance: 200, scrollType: 'once' });

    // 设置最大视频观看次数
    const maxVideoCount = 14;
    let videoCounter = 0;

    log(`将观看最多${maxVideoCount}个激励视频...`, 'info');
    while (videoCounter < maxVideoCount) {
      checkShouldExit();
      videoCounter++;

      log(`尝试观看第${videoCounter}个视频...`, 'info');
      const videoButtonEnabled = await this.agent.aiQuery('页面是否存在"看视频"按钮');

      if (videoButtonEnabled) {
        log('找到可点击的视频按钮', 'info');
        await this.agent.aiTap('"看视频"按钮');
        await this.watchVideoAd();

        // 处理视频观看后的确认
        const hasKnowButton = await this.agent.aiQuery('页面是否有"知道了"按钮');
        if (hasKnowButton) {
          log('点击"知道了"确认奖励', 'info');
          await this.agent.aiTap('知道了按钮');
        } else {
          await sleep(2000);
        }

        // 添加延迟避免频繁检查
        await sleep(1000);
      } else {
        log('没有更多可观看的视频，任务完成', 'info');
        break;
      }
    }

    log(`已完成${videoCounter}个视频观看任务`, 'success');
  }

  /**
   * 封装观看视频广告的公共方法
   */
  private async watchVideoAd(): Promise<void> {
    // 在长时间操作前检查是否需要退出
    checkShouldExit();

    log('等待广告加载和初始播放...', 'info');
    // 初始等待广告加载和播放
    await sleep(17000);

    // 循环检查是否可以关闭广告
    let attemptCount = 0;
    const maxAttempts = 10;

    while (attemptCount < maxAttempts) {
      checkShouldExit();
      attemptCount++;
      log(`尝试关闭广告 (${attemptCount}/${maxAttempts})...`, 'info');

      // 尝试点击跳过广告按钮
      await this.agent.aiTap('跳过广告或X按钮（注意不要把静音按钮误认为x键）');

      // 检查是否需要继续观看
      const needContinueWatching = await this.agent.aiQuery('页面是否出现"继续观看"按钮');
      if (needContinueWatching) {
        log('检测到"继续观看"按钮，点击继续...', 'info');
        await this.agent.aiTap('继续观看');
        await sleep(5000); // 继续观看后等待5秒
      } else {
        log('广告已关闭或跳过', 'success');
        break; // 没有继续观看按钮，退出循环
      }
    }
  }
}