import * as fs from 'fs';
import * as path from 'path';

const TMP_DATA_DIR = path.join(process.cwd(), 'reports', 'tmp_seo_data');

async function globalSetup(): Promise<void> {
  console.log('[SEO GlobalSetup] Preparing for test run...');
  
  if (fs.existsSync(TMP_DATA_DIR)) {
    console.log(`[SEO GlobalSetup] Found existing temporary data directory: ${TMP_DATA_DIR}. Cleaning it up...`);
    fs.rmSync(TMP_DATA_DIR, { recursive: true, force: true });
    console.log(`[SEO GlobalSetup] Temporary data directory cleaned.`);
  }
  
  fs.mkdirSync(TMP_DATA_DIR, { recursive: true });
  console.log(`[SEO GlobalSetup] Ensured temporary data directory exists and is empty: ${TMP_DATA_DIR}`);
  
  console.log('[SEO GlobalSetup] Preparation complete. Starting tests.');
}

export default globalSetup;